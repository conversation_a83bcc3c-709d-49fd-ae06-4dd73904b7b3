<?php
/* Smarty version 3.1.48, created on 2025-07-23 23:35:40
  from 'C:\xampp\htdocs\templates\widdx\includes\head.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_688155ac38bb93_91482617',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '182515f5adbeb0308c6355bf7aeaac907aeb9981' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\includes\\head.tpl',
      1 => 1753232436,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_688155ac38bb93_91482617 (Smarty_Internal_Template $_smarty_tpl) {
?>
<meta charset="<?php echo $_smarty_tpl->tpl_vars['charset']->value;?>
" />
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="description" content="<?php if ($_smarty_tpl->tpl_vars['metadescription']->value) {
echo $_smarty_tpl->tpl_vars['metadescription']->value;
} else { ?>WIDDX - Professional hosting solutions for your business<?php }?>">
<meta name="keywords" content="hosting, domains, web hosting, VPS, dedicated servers, WIDDX">
<meta name="author" content="WIDDX">

<link rel="icon" type="image/x-icon" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/assets/img/favicon.ico">
<link rel="shortcut icon" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/assets/img/favicon.ico">

<link href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/css/bootstrap.min.css" rel="stylesheet">

<link href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/css/style.css" rel="stylesheet">
<link href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/css/responsive.css" rel="stylesheet">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<meta name="theme-color" content="#2c3e50">
<meta name="msapplication-navbutton-color" content="#2c3e50">
<meta name="apple-mobile-web-app-status-bar-style" content="#2c3e50">

<meta property="og:title" content="<?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
 - <?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
">
<meta property="og:description" content="<?php if ($_smarty_tpl->tpl_vars['metadescription']->value) {
echo $_smarty_tpl->tpl_vars['metadescription']->value;
} else { ?>Professional hosting solutions for your business<?php }?>">
<meta property="og:type" content="website">
<meta property="og:url" content="<?php echo $_smarty_tpl->tpl_vars['currentpagelinkback']->value;?>
">
<meta property="og:image" content="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/assets/img/logo.png">
<meta property="og:site_name" content="<?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
">

<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="<?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
 - <?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
">
<meta name="twitter:description" content="<?php if ($_smarty_tpl->tpl_vars['metadescription']->value) {
echo $_smarty_tpl->tpl_vars['metadescription']->value;
} else { ?>Professional hosting solutions for your business<?php }?>">
<meta name="twitter:image" content="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/assets/img/logo.png">

<?php if ($_smarty_tpl->tpl_vars['token']->value) {?>
<meta name="csrf-token" content="<?php echo $_smarty_tpl->tpl_vars['token']->value;?>
">
<?php }?>

<style>
:root {
    --widdx-primary: #2c3e50;
    --widdx-secondary: #3498db;
    --widdx-accent: #e74c3c;
    --widdx-success: #27ae60;
    --widdx-warning: #f39c12;
    --widdx-dark: #34495e;
    --widdx-light: #ecf0f1;
    --widdx-white: #ffffff;
    --widdx-gray: #95a5a6;
    --widdx-text: #2c3e50;
    --widdx-bg: #ffffff;
    --widdx-border: #dee2e6;
}

[data-bs-theme="dark"] {
    --widdx-text: #ecf0f1;
    --widdx-bg: #2c3e50;
    --widdx-border: #495057;
    --widdx-light: #34495e;
    --widdx-dark: #ecf0f1;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--widdx-text);
    background-color: var(--widdx-bg);
    transition: background-color 0.3s ease, color 0.3s ease;
}
</style>

<?php echo '<script'; ?>
 src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
>
// Theme toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme from localStorage or default to light
    const savedTheme = localStorage.getItem('widdx-theme') || 'light';
    document.documentElement.setAttribute('data-bs-theme', savedTheme);
    
    // Update theme toggle button if exists
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.checked = savedTheme === 'dark';
    }
});

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-bs-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-bs-theme', newTheme);
    localStorage.setItem('widdx-theme', newTheme);
    
    // Update toggle button
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.checked = newTheme === 'dark';
    }
}
<?php echo '</script'; ?>
>
<?php }
}
