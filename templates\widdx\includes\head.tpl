{* WIDDX Independent Theme - HTML Head Section *}

{* Meta Tags *}
<meta charset="{$charset}" />
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="description" content="{if $metadescription}{$metadescription}{else}WIDDX - Professional hosting solutions for your business{/if}">
<meta name="keywords" content="hosting, domains, web hosting, VPS, dedicated servers, WIDDX">
<meta name="author" content="WIDDX">

{* Favicon *}
<link rel="icon" type="image/x-icon" href="{$WEB_ROOT}/templates/widdx/assets/img/favicon.ico">
<link rel="shortcut icon" href="{$WEB_ROOT}/templates/widdx/assets/img/favicon.ico">

{* Bootstrap 5.3+ CSS *}
<link href="{$WEB_ROOT}/templates/widdx/css/bootstrap.min.css" rel="stylesheet">

{* Custom Theme CSS *}
<link href="{$WEB_ROOT}/templates/widdx/css/style.css" rel="stylesheet">
<link href="{$WEB_ROOT}/templates/widdx/css/responsive.css" rel="stylesheet">

{* FontAwesome 6.4.0 *}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

{* Google Fonts *}
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

{* Theme Color for Mobile Browsers *}
<meta name="theme-color" content="#2c3e50">
<meta name="msapplication-navbutton-color" content="#2c3e50">
<meta name="apple-mobile-web-app-status-bar-style" content="#2c3e50">

{* Open Graph Meta Tags *}
<meta property="og:title" content="{$pagetitle} - {$companyname}">
<meta property="og:description" content="{if $metadescription}{$metadescription}{else}Professional hosting solutions for your business{/if}">
<meta property="og:type" content="website">
<meta property="og:url" content="{$currentpagelinkback}">
<meta property="og:image" content="{$WEB_ROOT}/templates/widdx/assets/img/logo.png">
<meta property="og:site_name" content="{$companyname}">

{* Twitter Card Meta Tags *}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{$pagetitle} - {$companyname}">
<meta name="twitter:description" content="{if $metadescription}{$metadescription}{else}Professional hosting solutions for your business{/if}">
<meta name="twitter:image" content="{$WEB_ROOT}/templates/widdx/assets/img/logo.png">

{* CSRF Token for WHMCS *}
{if $token}
<meta name="csrf-token" content="{$token}">
{/if}

{* Custom CSS for dark/light theme *}
<style>
:root {
    --widdx-primary: #2c3e50;
    --widdx-secondary: #3498db;
    --widdx-accent: #e74c3c;
    --widdx-success: #27ae60;
    --widdx-warning: #f39c12;
    --widdx-dark: #34495e;
    --widdx-light: #ecf0f1;
    --widdx-white: #ffffff;
    --widdx-gray: #95a5a6;
    --widdx-text: #2c3e50;
    --widdx-bg: #ffffff;
    --widdx-border: #dee2e6;
}

[data-bs-theme="dark"] {
    --widdx-text: #ecf0f1;
    --widdx-bg: #2c3e50;
    --widdx-border: #495057;
    --widdx-light: #34495e;
    --widdx-dark: #ecf0f1;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--widdx-text);
    background-color: var(--widdx-bg);
    transition: background-color 0.3s ease, color 0.3s ease;
}
</style>

{* jQuery 3.6.0 *}
<script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

{* Custom JavaScript for theme functionality *}
<script>
// Theme toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme from localStorage or default to light
    const savedTheme = localStorage.getItem('widdx-theme') || 'light';
    document.documentElement.setAttribute('data-bs-theme', savedTheme);
    
    // Update theme toggle button if exists
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.checked = savedTheme === 'dark';
    }
});

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-bs-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-bs-theme', newTheme);
    localStorage.setItem('widdx-theme', newTheme);
    
    // Update toggle button
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.checked = newTheme === 'dark';
    }
}
</script>
