{* WIDDX Independent Theme - Homepage Template *}

{include file="$template/header.tpl"}

{* Hero Section *}
<section class="widdx-hero">
    <div class="hero-background">
        <img src="{$WEB_ROOT}/templates/widdx/assets/img/hero-bg.jpg" alt="Hero Background" class="hero-bg-image">
        <div class="hero-overlay"></div>
    </div>
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title display-4 fw-bold mb-4">
                        Welcome to <span class="text-primary">WIDDX</span>
                    </h1>
                    <p class="hero-subtitle lead mb-4">
                        Your trusted partner for professional web hosting and domain services. 
                        Experience reliable, fast, and secure hosting solutions tailored for your business needs.
                    </p>
                    <div class="hero-actions d-flex flex-wrap gap-3">
                        <a href="{$WEB_ROOT}/cart.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-rocket me-2"></i>Get Started Today
                        </a>
                        <a href="{$WEB_ROOT}/contact.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-phone me-2"></i>Contact Sales
                        </a>
                    </div>
                    
                    {* Trust Indicators *}
                    <div class="hero-trust mt-4">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="trust-item">
                                    <h3 class="trust-number">99.9%</h3>
                                    <p class="trust-label">Uptime</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="trust-item">
                                    <h3 class="trust-number">24/7</h3>
                                    <p class="trust-label">Support</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="trust-item">
                                    <h3 class="trust-number">10K+</h3>
                                    <p class="trust-label">Customers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                {* Domain Search Widget *}
                {if $registerdomainenabled || $transferdomainenabled}
                    <div class="domain-search-widget">
                        <div class="card shadow-lg">
                            <div class="card-body p-4">
                                <h3 class="card-title text-center mb-4">Find Your Perfect Domain</h3>
                                <form method="post" action="domainchecker.php" id="frmDomainHomepage">
                                    <input type="hidden" name="transfer" />
                                    <div class="input-group input-group-lg mb-3">
                                        <input type="text" class="form-control" name="domain" 
                                               placeholder="{$LANG.exampledomain}" autocapitalize="none" 
                                               data-bs-toggle="tooltip" data-bs-placement="top" 
                                               title="{lang key='orderForm.required'}" />
                                        <button type="submit" class="btn btn-primary" id="btnDomainSearch">
                                            <i class="fas fa-search me-1"></i>{$LANG.search}
                                        </button>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        {if $registerdomainenabled}
                                            <button type="submit" class="btn btn-success" id="btnRegister">
                                                <i class="fas fa-plus me-1"></i>Register Domain
                                            </button>
                                        {/if}
                                        {if $transferdomainenabled}
                                            <button type="submit" class="btn btn-info" id="btnTransfer">
                                                <i class="fas fa-exchange-alt me-1"></i>{$LANG.domainstransfer}
                                            </button>
                                        {/if}
                                    </div>
                                    
                                    {include file="$template/includes/captcha.tpl"}
                                </form>
                                
                                {* Popular TLDs *}
                                <div class="popular-tlds mt-3">
                                    <small class="text-muted">Popular extensions:</small>
                                    <div class="tld-list mt-2">
                                        <span class="badge bg-light text-dark me-1">.com</span>
                                        <span class="badge bg-light text-dark me-1">.net</span>
                                        <span class="badge bg-light text-dark me-1">.org</span>
                                        <span class="badge bg-light text-dark me-1">.info</span>
                                        <span class="badge bg-light text-dark">.biz</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {/if}
            </div>
        </div>
    </div>
</section>

{* Quick Actions Section *}
<section class="widdx-quick-actions py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-12 mb-4">
                <h2 class="section-title">How Can We Help You Today?</h2>
                <p class="section-subtitle text-muted">Choose from our most popular services</p>
            </div>
        </div>
        <div class="row g-4">
            {if $registerdomainenabled || $transferdomainenabled}
                <div class="col-lg-3 col-md-6">
                    <a href="domainchecker.php" class="quick-action-card">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="quick-action-icon mb-3">
                                    <i class="fas fa-globe fa-3x text-primary"></i>
                                </div>
                                <h5 class="card-title">{$LANG.buyadomain}</h5>
                                <p class="card-text text-muted">Register your perfect domain name today</p>
                                <span class="btn btn-outline-primary">Get Started <i class="fas fa-arrow-right ms-1"></i></span>
                            </div>
                        </div>
                    </a>
                </div>
            {/if}
            
            <div class="col-lg-3 col-md-6">
                <a href="{$WEB_ROOT}/cart.php" class="quick-action-card">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="quick-action-icon mb-3">
                                <i class="fas fa-server fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">{$LANG.orderhosting}</h5>
                            <p class="card-text text-muted">Choose from our hosting plans</p>
                            <span class="btn btn-outline-success">Browse Plans <i class="fas fa-arrow-right ms-1"></i></span>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <a href="clientarea.php" class="quick-action-card">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="quick-action-icon mb-3">
                                <i class="fas fa-credit-card fa-3x text-warning"></i>
                            </div>
                            <h5 class="card-title">{$LANG.makepayment}</h5>
                            <p class="card-text text-muted">Pay your invoices securely online</p>
                            <span class="btn btn-outline-warning">Pay Now <i class="fas fa-arrow-right ms-1"></i></span>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <a href="submitticket.php" class="quick-action-card">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="quick-action-icon mb-3">
                                <i class="fas fa-life-ring fa-3x text-info"></i>
                            </div>
                            <h5 class="card-title">{$LANG.getsupport}</h5>
                            <p class="card-text text-muted">Get help from our expert team</p>
                            <span class="btn btn-outline-info">Contact Support <i class="fas fa-arrow-right ms-1"></i></span>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</section>

{* Features Section *}
<section class="widdx-features py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-md-12 text-center mb-5">
                <h2 class="section-title">Why Choose WIDDX?</h2>
                <p class="section-subtitle text-muted">We provide comprehensive hosting solutions with unmatched reliability</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-item text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-shield-alt fa-3x text-primary"></i>
                    </div>
                    <h4 class="feature-title">Secure & Reliable</h4>
                    <p class="feature-description">Advanced security measures and 99.9% uptime guarantee to keep your website safe and accessible.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-item text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-tachometer-alt fa-3x text-success"></i>
                    </div>
                    <h4 class="feature-title">Lightning Fast</h4>
                    <p class="feature-description">Optimized servers and SSD storage ensure your website loads quickly for the best user experience.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-item text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-headset fa-3x text-info"></i>
                    </div>
                    <h4 class="feature-title">24/7 Support</h4>
                    <p class="feature-description">Our expert support team is available around the clock to help you with any questions or issues.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-item text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-dollar-sign fa-3x text-warning"></i>
                    </div>
                    <h4 class="feature-title">Affordable Pricing</h4>
                    <p class="feature-description">Competitive pricing plans that scale with your business without compromising on quality.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-item text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-cogs fa-3x text-secondary"></i>
                    </div>
                    <h4 class="feature-title">Easy Management</h4>
                    <p class="feature-description">User-friendly control panel and one-click installations make managing your hosting simple.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-item text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-globe fa-3x text-danger"></i>
                    </div>
                    <h4 class="feature-title">Global Reach</h4>
                    <p class="feature-description">Multiple data centers worldwide ensure fast loading times for your global audience.</p>
                </div>
            </div>
        </div>
    </div>
</section>

{* News/Announcements Section *}
{if $twitterusername}
    <section class="widdx-twitter py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h2 class="section-title text-center mb-4">{$LANG.twitterlatesttweets}</h2>
                    <div id="twitterFeedOutput" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <script type="text/javascript" src="{assetPath file='twitter.js'}"></script>
                </div>
            </div>
        </div>
    </section>
{elseif $announcements}
    <section class="widdx-announcements py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h2 class="section-title text-center mb-4">{$LANG.news}</h2>
                </div>
            </div>
            <div class="row g-4">
                {foreach $announcements as $announcement}
                    {if $announcement@index < 3}
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <span class="badge bg-primary">
                                            {$carbon->translatePassedToFormat($announcement.rawDate, 'M j, Y')}
                                        </span>
                                    </div>
                                    <h5 class="card-title">
                                        <a href="{routePath('announcement-view', $announcement.id, $announcement.urlfriendlytitle)}" 
                                           class="text-decoration-none">
                                            {$announcement.title}
                                        </a>
                                    </h5>
                                    <p class="card-text text-muted">
                                        {if $announcement.text|strip_tags|strlen < 150}
                                            {$announcement.text|strip_tags}
                                        {else}
                                            {$announcement.summary}
                                        {/if}
                                    </p>
                                    <a href="{routePath('announcement-view', $announcement.id, $announcement.urlfriendlytitle)}" 
                                       class="btn btn-outline-primary btn-sm">
                                        {$LANG.readmore} <i class="fas fa-arrow-right ms-1"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    {/if}
                {/foreach}
            </div>
            {if count($announcements) > 3}
                <div class="text-center mt-4">
                    <a href="{$WEB_ROOT}/announcements.php" class="btn btn-primary">
                        View All News <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            {/if}
        </div>
    </section>
{/if}

{include file="$template/footer.tpl"}
