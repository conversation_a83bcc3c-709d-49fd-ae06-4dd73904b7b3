/**
 * WIDDX Independent Theme - WHMCS Integration JavaScript
 * Author: <PERSON>
 * Version: 1.0.0
 * Description: WHMCS-specific functionality and enhancements
 */

(function() {
    'use strict';

    const WHMCSIntegration = {
        // Initialize WHMCS-specific features
        init: function() {
            this.setupAjaxForms();
            this.setupCartEnhancements();
            this.setupDomainChecker();
            this.setupPasswordGenerator();
            this.setupInvoiceEnhancements();
            this.setupTicketEnhancements();
            this.setupClientAreaEnhancements();
            this.setupOrderFormEnhancements();
        },

        // Enhanced AJAX form handling
        setupAjaxForms: function() {
            // Override WHMCS modal handling
            $(document).on('click', '[data-toggle="modal"]', function(e) {
                const target = $(this).attr('data-target') || $(this).attr('href');
                const modal = $(target);
                
                if (modal.length && $(this).attr('data-remote')) {
                    modal.find('.modal-body').html('<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>');
                    modal.find('.modal-body').load($(this).attr('data-remote'));
                }
            });

            // Enhanced form submission with loading states
            $(document).on('submit', 'form[data-ajax="true"]', function(e) {
                e.preventDefault();
                const form = $(this);
                const submitBtn = form.find('[type="submit"]');
                const originalText = submitBtn.html();
                
                // Show loading state
                submitBtn.html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>Loading...');
                submitBtn.prop('disabled', true);
                
                $.ajax({
                    url: form.attr('action') || window.location.href,
                    method: form.attr('method') || 'POST',
                    data: form.serialize(),
                    success: function(response) {
                        // Handle success response
                        if (response.success) {
                            WHMCSIntegration.showNotification('success', response.message || 'Operation completed successfully');
                            if (response.redirect) {
                                window.location.href = response.redirect;
                            }
                        } else {
                            WHMCSIntegration.showNotification('error', response.message || 'An error occurred');
                        }
                    },
                    error: function() {
                        WHMCSIntegration.showNotification('error', 'An error occurred. Please try again.');
                    },
                    complete: function() {
                        // Restore button state
                        submitBtn.html(originalText);
                        submitBtn.prop('disabled', false);
                    }
                });
            });
        },

        // Shopping cart enhancements
        setupCartEnhancements: function() {
            // Auto-update cart totals
            $(document).on('change', '.cart-qty-input', function() {
                const input = $(this);
                const newQty = input.val();
                
                if (newQty > 0) {
                    WHMCSIntegration.updateCartQuantity(input.data('item-id'), newQty);
                }
            });

            // Remove cart item with confirmation
            $(document).on('click', '.remove-cart-item', function(e) {
                e.preventDefault();
                const link = $(this);
                
                if (confirm('Are you sure you want to remove this item from your cart?')) {
                    window.location.href = link.attr('href');
                }
            });

            // Promo code application
            $(document).on('submit', '#frmPromoCode', function(e) {
                e.preventDefault();
                const form = $(this);
                const promoCode = form.find('input[name="promocode"]').val();
                
                if (promoCode) {
                    WHMCSIntegration.applyPromoCode(promoCode);
                }
            });
        },

        // Domain checker enhancements
        setupDomainChecker: function() {
            const domainForm = $('#frmDomainHomepage, #frmDomainChecker');
            if (!domainForm.length) return;

            // Real-time domain validation
            domainForm.find('input[name="domain"]').on('input', function() {
                const domain = $(this).val().toLowerCase().trim();
                const input = $(this);
                
                // Remove previous validation classes
                input.removeClass('is-valid is-invalid');
                
                if (domain.length > 2) {
                    if (WHMCSIntegration.isValidDomain(domain)) {
                        input.addClass('is-valid');
                    } else {
                        input.addClass('is-invalid');
                    }
                }
            });

            // Enhanced domain search
            domainForm.on('submit', function(e) {
                const domain = $(this).find('input[name="domain"]').val();
                
                if (!WHMCSIntegration.isValidDomain(domain)) {
                    e.preventDefault();
                    WHMCSIntegration.showNotification('error', 'Please enter a valid domain name');
                    return false;
                }
                
                // Show loading state
                $(this).find('[type="submit"]').html('<span class="spinner-border spinner-border-sm me-2"></span>Searching...');
            });
        },

        // Password generator integration
        setupPasswordGenerator: function() {
            // Add generate password buttons to password fields
            $('input[type="password"]').each(function() {
                const input = $(this);
                if (input.attr('name') && input.attr('name').includes('password')) {
                    const generateBtn = $('<button type="button" class="btn btn-outline-secondary btn-sm ms-2" title="Generate Password"><i class="fas fa-key"></i></button>');
                    
                    generateBtn.on('click', function() {
                        const password = WHMCSIntegration.generatePassword();
                        input.val(password);
                        input.trigger('input'); // Trigger password strength check
                    });
                    
                    input.after(generateBtn);
                }
            });
        },

        // Invoice enhancements
        setupInvoiceEnhancements: function() {
            // Print invoice functionality
            $(document).on('click', '.print-invoice', function(e) {
                e.preventDefault();
                window.print();
            });

            // Payment method selection enhancement
            $(document).on('change', 'input[name="gateway"]', function() {
                const gateway = $(this).val();
                $('.payment-method-info').hide();
                $(`.payment-method-info[data-gateway="${gateway}"]`).show();
            });
        },

        // Support ticket enhancements
        setupTicketEnhancements: function() {
            // Auto-save ticket drafts
            const ticketForm = $('#frmTicket, #frmReply');
            if (ticketForm.length) {
                const messageField = ticketForm.find('textarea[name="message"]');
                
                if (messageField.length) {
                    messageField.on('input', WHMCSIntegration.utils.debounce(function() {
                        const content = $(this).val();
                        if (content.length > 10) {
                            localStorage.setItem('widdx_ticket_draft', content);
                        }
                    }, 1000));
                    
                    // Restore draft on page load
                    const draft = localStorage.getItem('widdx_ticket_draft');
                    if (draft && !messageField.val()) {
                        messageField.val(draft);
                    }
                }
            }

            // Clear draft on successful submission
            $(document).on('submit', '#frmTicket, #frmReply', function() {
                localStorage.removeItem('widdx_ticket_draft');
            });
        },

        // Client area enhancements
        setupClientAreaEnhancements: function() {
            // Service management quick actions
            $(document).on('click', '.service-action', function(e) {
                e.preventDefault();
                const action = $(this).data('action');
                const serviceId = $(this).data('service-id');
                
                if (confirm(`Are you sure you want to ${action} this service?`)) {
                    WHMCSIntegration.performServiceAction(serviceId, action);
                }
            });

            // Auto-refresh service status
            if ($('.service-status').length) {
                setInterval(function() {
                    WHMCSIntegration.refreshServiceStatus();
                }, 30000); // Refresh every 30 seconds
            }
        },

        // Order form enhancements
        setupOrderFormEnhancements: function() {
            // Product configuration updates
            $(document).on('change', '.product-config', function() {
                const config = $(this);
                const productId = config.data('product-id');
                
                WHMCSIntegration.updateProductPricing(productId);
            });

            // Billing cycle change
            $(document).on('change', 'select[name="billingcycle"]', function() {
                const cycle = $(this).val();
                WHMCSIntegration.updatePricingForCycle(cycle);
            });
        },

        // Utility functions
        updateCartQuantity: function(itemId, quantity) {
            $.post('cart.php', {
                a: 'update',
                i: itemId,
                qty: quantity
            }, function(response) {
                if (response.success) {
                    location.reload();
                }
            });
        },

        applyPromoCode: function(code) {
            $.post('cart.php', {
                a: 'promo',
                code: code
            }, function(response) {
                if (response.success) {
                    WHMCSIntegration.showNotification('success', 'Promo code applied successfully');
                    location.reload();
                } else {
                    WHMCSIntegration.showNotification('error', response.message || 'Invalid promo code');
                }
            });
        },

        isValidDomain: function(domain) {
            const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
            return domainRegex.test(domain);
        },

        generatePassword: function(length = 12) {
            const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
            let password = '';
            
            for (let i = 0; i < length; i++) {
                password += charset.charAt(Math.floor(Math.random() * charset.length));
            }
            
            return password;
        },

        performServiceAction: function(serviceId, action) {
            $.post('clientarea.php', {
                action: 'productdetails',
                id: serviceId,
                modop: action
            }, function(response) {
                if (response.success) {
                    WHMCSIntegration.showNotification('success', `Service ${action} completed successfully`);
                    location.reload();
                } else {
                    WHMCSIntegration.showNotification('error', response.message || 'Action failed');
                }
            });
        },

        refreshServiceStatus: function() {
            $('.service-status').each(function() {
                const statusElement = $(this);
                const serviceId = statusElement.data('service-id');
                
                $.get('clientarea.php', {
                    action: 'productdetails',
                    id: serviceId,
                    ajax: 'status'
                }, function(response) {
                    if (response.status) {
                        statusElement.html(`<span class="badge bg-${response.statusClass}">${response.status}</span>`);
                    }
                });
            });
        },

        updateProductPricing: function(productId) {
            const configData = {};
            $(`.product-config[data-product-id="${productId}"]`).each(function() {
                configData[$(this).attr('name')] = $(this).val();
            });
            
            $.post('cart.php', {
                a: 'confproduct',
                i: productId,
                ...configData
            }, function(response) {
                if (response.pricing) {
                    $('.product-pricing').html(response.pricing);
                }
            });
        },

        updatePricingForCycle: function(cycle) {
            $('.pricing-cycle').hide();
            $(`.pricing-cycle[data-cycle="${cycle}"]`).show();
        },

        showNotification: function(type, message) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
            
            const notification = $(`
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-${icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            
            $('body').append(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(function() {
                notification.alert('close');
            }, 5000);
        },

        utils: {
            debounce: function(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        WHMCSIntegration.init();
    });

    // Make WHMCSIntegration globally available
    window.WHMCSIntegration = WHMCSIntegration;

})();
