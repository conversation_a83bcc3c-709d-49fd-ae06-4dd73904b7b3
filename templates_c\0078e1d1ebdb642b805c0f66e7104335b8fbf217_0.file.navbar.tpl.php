<?php
/* Smarty version 3.1.48, created on 2025-07-23 23:35:40
  from 'C:\xampp\htdocs\templates\widdx\includes\navbar.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_688155ac9e4d51_26376886',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '0078e1d1ebdb642b805c0f66e7104335b8fbf217' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\includes\\navbar.tpl',
      1 => 1753232451,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_688155ac9e4d51_26376886 (Smarty_Internal_Template $_smarty_tpl) {
if ($_smarty_tpl->tpl_vars['navbar']->value) {?>
    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['navbar']->value, 'item');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
?>
        <?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>
            <li class="nav-item dropdown">
                <a href="<?php echo $_smarty_tpl->tpl_vars['item']->value->getUri();?>
" class="nav-link dropdown-toggle" 
                   data-bs-toggle="dropdown" aria-expanded="false"
                   <?php if ($_smarty_tpl->tpl_vars['item']->value->isHighlighted()) {?> style="color: var(--widdx-accent) !important;"<?php }?>>
                    <?php if ($_smarty_tpl->tpl_vars['item']->value->getClass()) {?>
                        <i class="<?php echo $_smarty_tpl->tpl_vars['item']->value->getClass();?>
"></i>
                    <?php }?>
                    <?php echo $_smarty_tpl->tpl_vars['item']->value->getLabel();?>

                </a>
                <ul class="dropdown-menu">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['item']->value->getChildren(), 'childItem');
$_smarty_tpl->tpl_vars['childItem']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['childItem']->value) {
$_smarty_tpl->tpl_vars['childItem']->do_else = false;
?>
                        <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getType() == "divider") {?>
                            <li><hr class="dropdown-divider"></li>
                        <?php } else { ?>
                            <li>
                                <a href="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getUri();?>
" class="dropdown-item"
                                   <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target')) {?> target="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target');?>
"<?php }?>
                                   <?php if ($_smarty_tpl->tpl_vars['childItem']->value->isHighlighted()) {?> style="color: var(--widdx-accent);"<?php }?>>
                                    <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getClass()) {?>
                                        <i class="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getClass();?>
"></i>
                                    <?php }?>
                                    <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getLabel();?>

                                    <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getBadge()) {?>
                                        <span class="badge bg-<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getBadge();?>
 ms-2"><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getBadgeText();?>
</span>
                                    <?php }?>
                                </a>
                            </li>
                        <?php }?>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </li>
        <?php } else { ?>
            <li class="nav-item">
                <a href="<?php echo $_smarty_tpl->tpl_vars['item']->value->getUri();?>
" class="nav-link"
                   <?php if ($_smarty_tpl->tpl_vars['item']->value->getAttribute('target')) {?> target="<?php echo $_smarty_tpl->tpl_vars['item']->value->getAttribute('target');?>
"<?php }?>
                   <?php if ($_smarty_tpl->tpl_vars['item']->value->isHighlighted()) {?> style="color: var(--widdx-accent) !important;"<?php }?>>
                    <?php if ($_smarty_tpl->tpl_vars['item']->value->getClass()) {?>
                        <i class="<?php echo $_smarty_tpl->tpl_vars['item']->value->getClass();?>
"></i>
                    <?php }?>
                    <?php echo $_smarty_tpl->tpl_vars['item']->value->getLabel();?>

                    <?php if ($_smarty_tpl->tpl_vars['item']->value->getBadge()) {?>
                        <span class="badge bg-<?php echo $_smarty_tpl->tpl_vars['item']->value->getBadge();?>
 ms-2"><?php echo $_smarty_tpl->tpl_vars['item']->value->getBadgeText();?>
</span>
                    <?php }?>
                </a>
            </li>
        <?php }?>
    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);
}
}
}
