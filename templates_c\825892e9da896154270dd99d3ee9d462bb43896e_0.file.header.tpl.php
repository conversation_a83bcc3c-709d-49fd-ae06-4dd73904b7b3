<?php
/* Smarty version 3.1.48, created on 2025-07-23 23:35:38
  from 'C:\xampp\htdocs\templates\widdx\header.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_688155aad53760_01503831',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '825892e9da896154270dd99d3ee9d462bb43896e' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\header.tpl',
      1 => 1753232537,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_688155aad53760_01503831 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE html>
<html lang="<?php echo $_smarty_tpl->tpl_vars['language']->value;?>
" data-bs-theme="light">
<head>
    <title><?php if ($_smarty_tpl->tpl_vars['kbarticle']->value['title']) {
echo $_smarty_tpl->tpl_vars['kbarticle']->value['title'];?>
 - <?php }
echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
 - <?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
</title>
    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/head.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
    <?php echo $_smarty_tpl->tpl_vars['headoutput']->value;?>

</head>
<body data-phone-cc-input="<?php echo $_smarty_tpl->tpl_vars['phoneNumberInputStyle']->value;?>
" class="widdx-body">

<?php if ($_smarty_tpl->tpl_vars['captcha']->value) {
echo $_smarty_tpl->tpl_vars['captcha']->value->getMarkup();
}
echo $_smarty_tpl->tpl_vars['headeroutput']->value;?>


<nav class="navbar navbar-expand-lg widdx-top-nav">
    <div class="container">
                <?php if ($_smarty_tpl->tpl_vars['languagechangeenabled']->value && count($_smarty_tpl->tpl_vars['locales']->value) > 1) {?>
            <div class="dropdown me-3">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-globe me-1"></i>
                    <?php echo $_smarty_tpl->tpl_vars['activeLocale']->value['localisedName'];?>

                </button>
                <ul class="dropdown-menu">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['locales']->value, 'locale');
$_smarty_tpl->tpl_vars['locale']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['locale']->value) {
$_smarty_tpl->tpl_vars['locale']->do_else = false;
?>
                        <li>
                            <a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['currentpagelinkback']->value;?>
language=<?php echo $_smarty_tpl->tpl_vars['locale']->value['language'];?>
">
                                <?php echo $_smarty_tpl->tpl_vars['locale']->value['localisedName'];?>

                            </a>
                        </li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>
        <?php }?>

                <div class="navbar-nav ms-auto">
            <?php if ($_smarty_tpl->tpl_vars['loggedin']->value) {?>
                                <div class="nav-item dropdown me-2">
                    <a class="nav-link position-relative" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bell"></i>
                        <?php if (count($_smarty_tpl->tpl_vars['clientAlerts']->value) > 0) {?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?php echo count($_smarty_tpl->tpl_vars['clientAlerts']->value);?>

                            </span>
                        <?php }?>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end widdx-notifications">
                        <h6 class="dropdown-header"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['notifications'];?>
</h6>
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['clientAlerts']->value, 'alert');
$_smarty_tpl->tpl_vars['alert']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['alert']->value) {
$_smarty_tpl->tpl_vars['alert']->do_else = false;
?>
                            <a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['alert']->value->getLink();?>
">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-<?php if ($_smarty_tpl->tpl_vars['alert']->value->getSeverity() == 'danger') {?>exclamation-circle text-danger<?php } elseif ($_smarty_tpl->tpl_vars['alert']->value->getSeverity() == 'warning') {?>exclamation-triangle text-warning<?php } elseif ($_smarty_tpl->tpl_vars['alert']->value->getSeverity() == 'info') {?>info-circle text-info<?php } else { ?>check-circle text-success<?php }?> me-2 mt-1"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-semibold"><?php echo $_smarty_tpl->tpl_vars['alert']->value->getMessage();?>
</div>
                                    </div>
                                </div>
                            </a>
                        <?php
}
if ($_smarty_tpl->tpl_vars['alert']->do_else) {
?>
                            <div class="dropdown-item-text text-muted">
                                <?php echo $_smarty_tpl->tpl_vars['LANG']->value['notificationsnone'];?>

                            </div>
                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                    </div>
                </div>

                                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo $_smarty_tpl->tpl_vars['LANG']->value['account'];?>

                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/clientarea.php">
                            <i class="fas fa-tachometer-alt me-2"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['clientareanavhome'];?>

                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/clientarea.php?action=details">
                            <i class="fas fa-user me-2"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['clientareanavdetails'];?>

                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/clientarea.php?action=products">
                            <i class="fas fa-box me-2"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['clientareanavservices'];?>

                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/clientarea.php?action=domains">
                            <i class="fas fa-globe me-2"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['clientareanavdomains'];?>

                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['clientareanavlogout'];?>

                        </a></li>
                    </ul>
                </div>
            <?php } else { ?>
                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/clientarea.php" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-sign-in-alt me-1"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['login'];?>

                </a>
                <?php if ($_smarty_tpl->tpl_vars['condlinks']->value['allowClientRegistration']) {?>
                    <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/register.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-user-plus me-1"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['register'];?>

                    </a>
                <?php }?>
            <?php }?>

                        <div class="nav-item ms-2">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="theme-toggle" onchange="toggleTheme()">
                    <label class="form-check-label" for="theme-toggle">
                        <i class="fas fa-moon"></i>
                    </label>
                </div>
            </div>
        </div>

                <?php if ($_smarty_tpl->tpl_vars['adminMasqueradingAsClient']->value || $_smarty_tpl->tpl_vars['adminLoggedIn']->value) {?>
            <div class="position-fixed top-0 start-50 translate-middle-x">
                <div class="alert alert-warning alert-dismissible fade show mt-2" role="alert">
                    <i class="fas fa-user-shield me-2"></i>
                    <?php if ($_smarty_tpl->tpl_vars['adminMasqueradingAsClient']->value) {?>
                        <?php echo $_smarty_tpl->tpl_vars['LANG']->value['adminmasqueradingasclient'];?>

                    <?php } else { ?>
                        <?php echo $_smarty_tpl->tpl_vars['LANG']->value['adminloggedin'];?>

                    <?php }?>
                    <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/logout.php?returntoadmin=1" class="btn btn-sm btn-outline-warning ms-2">
                        <?php echo $_smarty_tpl->tpl_vars['LANG']->value['returntoadminarea'];?>

                    </a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        <?php }?>
    </div>
</nav>

<nav class="navbar navbar-expand-lg widdx-main-nav">
    <div class="container">
                <a class="navbar-brand" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/index.php">
            <img src="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/assets/img/logo.png" alt="<?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
" class="widdx-logo">
        </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#widdx-navbar" 
                aria-controls="widdx-navbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

                <div class="collapse navbar-collapse" id="widdx-navbar">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/index.php">
                        <i class="fas fa-home me-1"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['home'];?>

                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-server me-1"></i>Hosting
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php">Web Hosting</a></li>
                        <li><a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php">VPS Hosting</a></li>
                        <li><a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php">Dedicated Servers</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/domainchecker.php">Domain Registration</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/knowledgebase.php">
                        <i class="fas fa-book me-1"></i>Knowledge Base
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/submitticket.php">
                        <i class="fas fa-life-ring me-1"></i>Support
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/contact.php">
                        <i class="fas fa-envelope me-1"></i>Contact
                    </a>
                </li>

                                <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/navbar.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('navbar'=>$_smarty_tpl->tpl_vars['primaryNavbar']->value), 0, true);
?>
            </ul>

                        <ul class="navbar-nav">
                <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/navbar.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('navbar'=>$_smarty_tpl->tpl_vars['secondaryNavbar']->value), 0, true);
?>
                
                                <li class="nav-item">
                    <a class="nav-link" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=view">
                        <i class="fas fa-shopping-cart me-1"></i>
                        <?php echo $_smarty_tpl->tpl_vars['LANG']->value['viewcart'];?>

                        <?php if ($_smarty_tpl->tpl_vars['cartitemcount']->value > 0) {?>
                            <span class="badge bg-primary ms-1"><?php echo $_smarty_tpl->tpl_vars['cartitemcount']->value;?>
</span>
                        <?php }?>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/validateuser.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
$_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/verifyemail.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

<main class="widdx-main-content">
    <div class="container-fluid">
        <div class="row">
                        <?php if (!$_smarty_tpl->tpl_vars['inShoppingCart']->value && ($_smarty_tpl->tpl_vars['primarySidebar']->value->hasChildren() || $_smarty_tpl->tpl_vars['secondarySidebar']->value->hasChildren())) {?>
                <div class="col-lg-3 col-xl-2 widdx-sidebar-container">
                    <?php if ($_smarty_tpl->tpl_vars['primarySidebar']->value->hasChildren()) {?>
                        <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/sidebar.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('sidebar'=>$_smarty_tpl->tpl_vars['primarySidebar']->value), 0, true);
?>
                    <?php }?>
                    <?php if ($_smarty_tpl->tpl_vars['secondarySidebar']->value->hasChildren()) {?>
                        <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/sidebar.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('sidebar'=>$_smarty_tpl->tpl_vars['secondarySidebar']->value), 0, true);
?>
                    <?php }?>
                </div>
                <div class="col-lg-9 col-xl-10">
                    <?php if (!$_smarty_tpl->tpl_vars['skipMainBodyContainer']->value) {?>
                        <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/pageheader.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('title'=>$_smarty_tpl->tpl_vars['displayTitle']->value,'desc'=>$_smarty_tpl->tpl_vars['tagline']->value,'showbreadcrumb'=>true), 0, true);
?>
                    <?php }?>
            <?php } else { ?>
                <div class="col-12">
                    <?php if (!$_smarty_tpl->tpl_vars['showingLoginPage']->value && !$_smarty_tpl->tpl_vars['inShoppingCart']->value && $_smarty_tpl->tpl_vars['templatefile']->value != 'homepage' && !$_smarty_tpl->tpl_vars['skipMainBodyContainer']->value) {?>
                        <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/pageheader.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('title'=>$_smarty_tpl->tpl_vars['displayTitle']->value,'desc'=>$_smarty_tpl->tpl_vars['tagline']->value,'showbreadcrumb'=>true), 0, true);
?>
                    <?php }?>
            <?php }?>

                        <div class="widdx-content-wrapper"><?php }
}
