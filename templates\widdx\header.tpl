<!DOCTYPE html>
<html lang="{$language}" data-bs-theme="light">
<head>
    <title>{if $kbarticle.title}{$kbarticle.title} - {/if}{$pagetitle} - {$companyname}</title>
    {include file="$template/includes/head.tpl"}
    {$headoutput}
</head>
<body data-phone-cc-input="{$phoneNumberInputStyle}" class="widdx-body">

{* WHMCS Captcha and Header Output *}
{if $captcha}{$captcha->getMarkup()}{/if}
{$headeroutput}

{* Top Navigation Bar *}
<nav class="navbar navbar-expand-lg widdx-top-nav">
    <div class="container">
        {* Language Selector *}
        {if $languagechangeenabled && count($locales) > 1}
            <div class="dropdown me-3">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-globe me-1"></i>
                    {$activeLocale.localisedName}
                </button>
                <ul class="dropdown-menu">
                    {foreach $locales as $locale}
                        <li>
                            <a class="dropdown-item" href="{$currentpagelinkback}language={$locale.language}">
                                {$locale.localisedName}
                            </a>
                        </li>
                    {/foreach}
                </ul>
            </div>
        {/if}

        {* User Account Menu *}
        <div class="navbar-nav ms-auto">
            {if $loggedin}
                {* Notifications *}
                <div class="nav-item dropdown me-2">
                    <a class="nav-link position-relative" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bell"></i>
                        {if count($clientAlerts) > 0}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {count($clientAlerts)}
                            </span>
                        {/if}
                    </a>
                    <div class="dropdown-menu dropdown-menu-end widdx-notifications">
                        <h6 class="dropdown-header">{$LANG.notifications}</h6>
                        {foreach $clientAlerts as $alert}
                            <a class="dropdown-item" href="{$alert->getLink()}">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-{if $alert->getSeverity() == 'danger'}exclamation-circle text-danger{elseif $alert->getSeverity() == 'warning'}exclamation-triangle text-warning{elseif $alert->getSeverity() == 'info'}info-circle text-info{else}check-circle text-success{/if} me-2 mt-1"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-semibold">{$alert->getMessage()}</div>
                                    </div>
                                </div>
                            </a>
                        {foreachelse}
                            <div class="dropdown-item-text text-muted">
                                {$LANG.notificationsnone}
                            </div>
                        {/foreach}
                    </div>
                </div>

                {* User Menu *}
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-1"></i>
                        {$LANG.account}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{$WEB_ROOT}/clientarea.php">
                            <i class="fas fa-tachometer-alt me-2"></i>{$LANG.clientareanavhome}
                        </a></li>
                        <li><a class="dropdown-item" href="{$WEB_ROOT}/clientarea.php?action=details">
                            <i class="fas fa-user me-2"></i>{$LANG.clientareanavdetails}
                        </a></li>
                        <li><a class="dropdown-item" href="{$WEB_ROOT}/clientarea.php?action=products">
                            <i class="fas fa-box me-2"></i>{$LANG.clientareanavservices}
                        </a></li>
                        <li><a class="dropdown-item" href="{$WEB_ROOT}/clientarea.php?action=domains">
                            <i class="fas fa-globe me-2"></i>{$LANG.clientareanavdomains}
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{$WEB_ROOT}/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>{$LANG.clientareanavlogout}
                        </a></li>
                    </ul>
                </div>
            {else}
                <a href="{$WEB_ROOT}/clientarea.php" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-sign-in-alt me-1"></i>{$LANG.login}
                </a>
                {if $condlinks.allowClientRegistration}
                    <a href="{$WEB_ROOT}/register.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-user-plus me-1"></i>{$LANG.register}
                    </a>
                {/if}
            {/if}

            {* Theme Toggle *}
            <div class="nav-item ms-2">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="theme-toggle" onchange="toggleTheme()">
                    <label class="form-check-label" for="theme-toggle">
                        <i class="fas fa-moon"></i>
                    </label>
                </div>
            </div>
        </div>

        {* Admin Masquerading Notice *}
        {if $adminMasqueradingAsClient || $adminLoggedIn}
            <div class="position-fixed top-0 start-50 translate-middle-x">
                <div class="alert alert-warning alert-dismissible fade show mt-2" role="alert">
                    <i class="fas fa-user-shield me-2"></i>
                    {if $adminMasqueradingAsClient}
                        {$LANG.adminmasqueradingasclient}
                    {else}
                        {$LANG.adminloggedin}
                    {/if}
                    <a href="{$WEB_ROOT}/logout.php?returntoadmin=1" class="btn btn-sm btn-outline-warning ms-2">
                        {$LANG.returntoadminarea}
                    </a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        {/if}
    </div>
</nav>

{* Main Navigation *}
<nav class="navbar navbar-expand-lg widdx-main-nav">
    <div class="container">
        {* Brand Logo *}
        <a class="navbar-brand" href="{$WEB_ROOT}/index.php">
            <img src="{$WEB_ROOT}/templates/widdx/assets/img/logo.png" alt="{$companyname}" class="widdx-logo">
        </a>

        {* Mobile Toggle *}
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#widdx-navbar" 
                aria-controls="widdx-navbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        {* Navigation Menu *}
        <div class="collapse navbar-collapse" id="widdx-navbar">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="{$WEB_ROOT}/index.php">
                        <i class="fas fa-home me-1"></i>{$LANG.home}
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-server me-1"></i>Hosting
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{$WEB_ROOT}/cart.php">Web Hosting</a></li>
                        <li><a class="dropdown-item" href="{$WEB_ROOT}/cart.php">VPS Hosting</a></li>
                        <li><a class="dropdown-item" href="{$WEB_ROOT}/cart.php">Dedicated Servers</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{$WEB_ROOT}/domainchecker.php">Domain Registration</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{$WEB_ROOT}/knowledgebase.php">
                        <i class="fas fa-book me-1"></i>Knowledge Base
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{$WEB_ROOT}/submitticket.php">
                        <i class="fas fa-life-ring me-1"></i>Support
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{$WEB_ROOT}/contact.php">
                        <i class="fas fa-envelope me-1"></i>Contact
                    </a>
                </li>

                {* Include Primary Navbar Items *}
                {include file="$template/includes/navbar.tpl" navbar=$primaryNavbar}
            </ul>

            {* Secondary Navigation *}
            <ul class="navbar-nav">
                {include file="$template/includes/navbar.tpl" navbar=$secondaryNavbar}
                
                {* Shopping Cart *}
                <li class="nav-item">
                    <a class="nav-link" href="{$WEB_ROOT}/cart.php?a=view">
                        <i class="fas fa-shopping-cart me-1"></i>
                        {$LANG.viewcart}
                        {if $cartitemcount > 0}
                            <span class="badge bg-primary ms-1">{$cartitemcount}</span>
                        {/if}
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

{* Include WHMCS validation and verification templates *}
{include file="$template/includes/validateuser.tpl"}
{include file="$template/includes/verifyemail.tpl"}

{* Main Content Container *}
<main class="widdx-main-content">
    <div class="container-fluid">
        <div class="row">
            {* Sidebar *}
            {if !$inShoppingCart && ($primarySidebar->hasChildren() || $secondarySidebar->hasChildren())}
                <div class="col-lg-3 col-xl-2 widdx-sidebar-container">
                    {if $primarySidebar->hasChildren()}
                        {include file="$template/includes/sidebar.tpl" sidebar=$primarySidebar}
                    {/if}
                    {if $secondarySidebar->hasChildren()}
                        {include file="$template/includes/sidebar.tpl" sidebar=$secondarySidebar}
                    {/if}
                </div>
                <div class="col-lg-9 col-xl-10">
                    {if !$skipMainBodyContainer}
                        {include file="$template/includes/pageheader.tpl" title=$displayTitle desc=$tagline showbreadcrumb=true}
                    {/if}
            {else}
                <div class="col-12">
                    {if !$showingLoginPage && !$inShoppingCart && $templatefile != 'homepage' && !$skipMainBodyContainer}
                        {include file="$template/includes/pageheader.tpl" title=$displayTitle desc=$tagline showbreadcrumb=true}
                    {/if}
            {/if}

            {* Main Content Area *}
            <div class="widdx-content-wrapper">{* Content will be inserted here by individual templates *}
