                </div><!-- /.main-content -->
                {if !$inShoppingCart && $secondarySidebar->hasChildren()}
                    <div class="col-md-3 pull-md-left sidebar sidebar-secondary">
                        {include file="$template/includes/sidebar.tpl" sidebar=$secondarySidebar}
                    </div>
                {/if}
            <div class="clearfix"></div>
        </div>
    </div>
</section>

{* WIDDX Custom Footer *}
<section id="footer" class="widdx-footer">
    <div class="container">
        <div class="row">
            <div class="col-md-4 col-sm-6">
                <div class="footer-section">
                    <h4>WIDDX</h4>
                    <p>Your trusted partner for professional web hosting and domain services. We provide reliable, fast, and secure hosting solutions for businesses worldwide.</p>
                    <div class="footer-logo">
                        <img src="{$WEB_ROOT}/templates/widdx/assets/img/logo.png" alt="WIDDX" class="footer-logo-img">
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="footer-section">
                    <h5>Services</h5>
                    <ul class="footer-links">
                        <li><a href="{$WEB_ROOT}/cart.php">Web Hosting</a></li>
                        <li><a href="{$WEB_ROOT}/cart.php">VPS Hosting</a></li>
                        <li><a href="{$WEB_ROOT}/cart.php">Dedicated Servers</a></li>
                        <li><a href="{$WEB_ROOT}/domainchecker.php">Domain Registration</a></li>
                    </ul>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="footer-section">
                    <h5>Support</h5>
                    <ul class="footer-links">
                        <li><a href="{$WEB_ROOT}/knowledgebase.php">Knowledge Base</a></li>
                        <li><a href="{$WEB_ROOT}/submitticket.php">Submit Ticket</a></li>
                        <li><a href="{$WEB_ROOT}/supporttickets.php">My Tickets</a></li>
                        <li><a href="{$WEB_ROOT}/contact.php">Contact Us</a></li>
                    </ul>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="footer-section">
                    <h5>Account</h5>
                    <ul class="footer-links">
                        <li><a href="{$WEB_ROOT}/clientarea.php">Client Area</a></li>
                        <li><a href="{$WEB_ROOT}/register.php">Register</a></li>
                        <li><a href="{$WEB_ROOT}/pwreset.php">Forgot Password</a></li>
                        <li><a href="{$WEB_ROOT}/affiliates.php">Affiliate Program</a></li>
                    </ul>
                </div>
            </div>
            <div class="col-md-2 col-sm-12">
                <div class="footer-section">
                    <h5>Follow Us</h5>
                    <div class="social-links">
                        <a href="#" class="social-link facebook" title="Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link twitter" title="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link linkedin" title="LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="social-link instagram" title="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link youtube" title="YouTube">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <hr class="footer-divider">
        
        <div class="row">
            <div class="col-md-8">
                <div class="footer-bottom">
                    <p class="copyright">
                        &copy; {$date_year} {$companyname}. All rights reserved. 
                        Powered by <strong>WIDDX</strong> - Professional Hosting Solutions.
                    </p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="footer-bottom text-right">
                    <ul class="footer-bottom-links">
                        <li><a href="{$WEB_ROOT}/legal.php">Terms of Service</a></li>
                        <li><a href="{$WEB_ROOT}/privacy.php">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <a href="#" class="back-to-top widdx-back-to-top">
            <i class="fas fa-chevron-up"></i>
        </a>
    </div>
</section>

<div id="fullpage-overlay" class="hidden">
    <div class="outer-wrapper">
        <div class="inner-wrapper">
            <img src="{$WEB_ROOT}/assets/img/overlay-spinner.svg">
            <br>
            <span class="msg"></span>
        </div>
    </div>
</div>

<div class="modal system-modal fade" id="modalAjax" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content panel-primary">
            <div class="modal-header panel-heading">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                    <span class="sr-only">{$LANG.close}</span>
                </button>
                <h4 class="modal-title"></h4>
            </div>
            <div class="modal-body panel-body">
                {$LANG.loading}
            </div>
            <div class="modal-footer panel-footer">
                <div class="pull-left loader">
                    <i class="fas fa-circle-notch fa-spin"></i>
                    {$LANG.loading}
                </div>
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    {$LANG.close}
                </button>
                <button type="button" class="btn btn-primary modal-submit">
                    {$LANG.submit}
                </button>
            </div>
        </div>
    </div>
</div>

{include file="$template/includes/generate-password.tpl"}
{$footeroutput}

</body>
</html>
