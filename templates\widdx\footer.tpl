            </div>{* End content wrapper *}
        </div>{* End sidebar column *}
    </div>{* End row *}
</div>{* End container-fluid *}
</main>{* End main content *}

{* WIDDX Footer *}
<footer class="widdx-footer">
    <div class="container">
        <div class="row">
            {* Company Information *}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="footer-section">
                    <img src="{$WEB_ROOT}/templates/widdx/assets/img/logo.png" alt="{$companyname}" class="footer-logo mb-3">
                    <h5 class="footer-title">WIDDX</h5>
                    <p class="footer-text">
                        Your trusted partner for professional web hosting and domain services. 
                        We provide reliable, fast, and secure hosting solutions for businesses worldwide.
                    </p>
                    <div class="footer-contact">
                        {if $systemurl}
                            <p class="mb-1">
                                <i class="fas fa-globe me-2"></i>
                                <a href="{$systemurl}" class="text-decoration-none">{$systemurl}</a>
                            </p>
                        {/if}
                        {if $companyemail}
                            <p class="mb-1">
                                <i class="fas fa-envelope me-2"></i>
                                <a href="mailto:{$companyemail}" class="text-decoration-none">{$companyemail}</a>
                            </p>
                        {/if}
                        {if $companyphonenumber}
                            <p class="mb-1">
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:{$companyphonenumber}" class="text-decoration-none">{$companyphonenumber}</a>
                            </p>
                        {/if}
                    </div>
                </div>
            </div>

            {* Quick Links *}
            <div class="col-lg-2 col-md-6 mb-4">
                <div class="footer-section">
                    <h6 class="footer-title">Services</h6>
                    <ul class="footer-links">
                        <li><a href="{$WEB_ROOT}/cart.php">Web Hosting</a></li>
                        <li><a href="{$WEB_ROOT}/cart.php">VPS Hosting</a></li>
                        <li><a href="{$WEB_ROOT}/cart.php">Dedicated Servers</a></li>
                        <li><a href="{$WEB_ROOT}/domainchecker.php">Domain Registration</a></li>
                        <li><a href="{$WEB_ROOT}/cart.php">SSL Certificates</a></li>
                    </ul>
                </div>
            </div>

            {* Support Links *}
            <div class="col-lg-2 col-md-6 mb-4">
                <div class="footer-section">
                    <h6 class="footer-title">Support</h6>
                    <ul class="footer-links">
                        <li><a href="{$WEB_ROOT}/knowledgebase.php">Knowledge Base</a></li>
                        <li><a href="{$WEB_ROOT}/submitticket.php">Submit Ticket</a></li>
                        <li><a href="{$WEB_ROOT}/supporttickets.php">My Tickets</a></li>
                        <li><a href="{$WEB_ROOT}/contact.php">Contact Us</a></li>
                        <li><a href="{$WEB_ROOT}/serverstatus.php">Server Status</a></li>
                    </ul>
                </div>
            </div>

            {* Account Links *}
            <div class="col-lg-2 col-md-6 mb-4">
                <div class="footer-section">
                    <h6 class="footer-title">Account</h6>
                    <ul class="footer-links">
                        <li><a href="{$WEB_ROOT}/clientarea.php">Client Area</a></li>
                        {if $condlinks.allowClientRegistration}
                            <li><a href="{$WEB_ROOT}/register.php">Register</a></li>
                        {/if}
                        <li><a href="{$WEB_ROOT}/pwreset.php">Forgot Password</a></li>
                        <li><a href="{$WEB_ROOT}/affiliates.php">Affiliate Program</a></li>
                        <li><a href="{$WEB_ROOT}/announcements.php">News</a></li>
                    </ul>
                </div>
            </div>

            {* Social Media & Newsletter *}
            <div class="col-lg-2 col-md-12 mb-4">
                <div class="footer-section">
                    <h6 class="footer-title">Follow Us</h6>
                    <div class="social-links mb-3">
                        <a href="#" class="social-link facebook" title="Facebook" target="_blank">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link twitter" title="Twitter" target="_blank">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link linkedin" title="LinkedIn" target="_blank">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="social-link instagram" title="Instagram" target="_blank">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link youtube" title="YouTube" target="_blank">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                    
                    {* Newsletter Signup *}
                    <div class="newsletter-signup">
                        <h6 class="footer-title">Newsletter</h6>
                        <form class="newsletter-form">
                            <div class="input-group input-group-sm">
                                <input type="email" class="form-control" placeholder="Your email" required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        {* Footer Bottom *}
        <hr class="footer-divider">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="footer-bottom">
                    <p class="copyright mb-0">
                        &copy; {$date_year} {$companyname}. All rights reserved. 
                        Powered by <strong>WIDDX</strong> - Professional Hosting Solutions.
                    </p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="footer-bottom text-md-end">
                    <ul class="footer-bottom-links">
                        <li><a href="{$WEB_ROOT}/legal.php">Terms of Service</a></li>
                        <li><a href="{$WEB_ROOT}/privacy.php">Privacy Policy</a></li>
                        <li><a href="{$WEB_ROOT}/gdpr.php">GDPR</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    {* Back to Top Button *}
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
        <i class="fas fa-chevron-up"></i>
    </button>
</footer>

{* WHMCS Modal and Overlay Elements *}
<div id="fullpage-overlay" class="d-none">
    <div class="d-flex justify-content-center align-items-center h-100">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-3">
                <span class="loading-text">Loading...</span>
            </div>
        </div>
    </div>
</div>

{* WHMCS System Modal *}
<div class="modal fade" id="modalAjax" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">{$LANG.loading}</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {$LANG.close}
                </button>
                <button type="button" class="btn btn-primary modal-submit">
                    {$LANG.submit}
                </button>
            </div>
        </div>
    </div>
</div>

{* Include WHMCS Generate Password Template *}
{include file="$template/includes/generate-password.tpl"}

{* Bootstrap 5.3+ JavaScript *}
<script src="{$WEB_ROOT}/templates/widdx/js/bootstrap.bundle.min.js"></script>

{* Custom Theme JavaScript *}
<script src="{$WEB_ROOT}/templates/widdx/js/theme.js"></script>
<script src="{$WEB_ROOT}/templates/widdx/js/whmcs-integration.js"></script>

{* WHMCS Footer Output *}
{$footeroutput}

{* Custom JavaScript for enhanced functionality *}
<script>
// Back to top functionality
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Show/hide back to top button
window.addEventListener('scroll', function() {
    const backToTop = document.getElementById('backToTop');
    if (window.pageYOffset > 300) {
        backToTop.classList.add('show');
    } else {
        backToTop.classList.remove('show');
    }
});

// Newsletter form submission
document.querySelector('.newsletter-form')?.addEventListener('submit', function(e) {
    e.preventDefault();
    const email = this.querySelector('input[type="email"]').value;
    if (email) {
        // Add your newsletter subscription logic here
        alert('Thank you for subscribing to our newsletter!');
        this.reset();
    }
});

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

</body>
</html>
