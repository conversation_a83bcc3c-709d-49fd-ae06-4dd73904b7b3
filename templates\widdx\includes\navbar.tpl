{* WIDDX Independent Theme - Navigation Bar Component *}

{if $navbar}
    {foreach $navbar as $item}
        {if $item->hasChildren()}
            <li class="nav-item dropdown">
                <a href="{$item->getUri()}" class="nav-link dropdown-toggle" 
                   data-bs-toggle="dropdown" aria-expanded="false"
                   {if $item->isHighlighted()} style="color: var(--widdx-accent) !important;"{/if}>
                    {if $item->getClass()}
                        <i class="{$item->getClass()}"></i>
                    {/if}
                    {$item->getLabel()}
                </a>
                <ul class="dropdown-menu">
                    {foreach $item->getChildren() as $childItem}
                        {if $childItem->getType() eq "divider"}
                            <li><hr class="dropdown-divider"></li>
                        {else}
                            <li>
                                <a href="{$childItem->getUri()}" class="dropdown-item"
                                   {if $childItem->getAttribute('target')} target="{$childItem->getAttribute('target')}"{/if}
                                   {if $childItem->isHighlighted()} style="color: var(--widdx-accent);"{/if}>
                                    {if $childItem->getClass()}
                                        <i class="{$childItem->getClass()}"></i>
                                    {/if}
                                    {$childItem->getLabel()}
                                    {if $childItem->getBadge()}
                                        <span class="badge bg-{$childItem->getBadge()} ms-2">{$childItem->getBadgeText()}</span>
                                    {/if}
                                </a>
                            </li>
                        {/if}
                    {/foreach}
                </ul>
            </li>
        {else}
            <li class="nav-item">
                <a href="{$item->getUri()}" class="nav-link"
                   {if $item->getAttribute('target')} target="{$item->getAttribute('target')}"{/if}
                   {if $item->isHighlighted()} style="color: var(--widdx-accent) !important;"{/if}>
                    {if $item->getClass()}
                        <i class="{$item->getClass()}"></i>
                    {/if}
                    {$item->getLabel()}
                    {if $item->getBadge()}
                        <span class="badge bg-{$item->getBadge()} ms-2">{$item->getBadgeText()}</span>
                    {/if}
                </a>
            </li>
        {/if}
    {/foreach}
{/if}
