/**
 * WIDDX Independent Theme - Main JavaScript
 * Author: <PERSON>
 * Version: 1.0.0
 * Description: Core theme functionality and enhancements
 */

(function() {
    'use strict';

    // Theme configuration
    const WIDDX = {
        config: {
            animationDuration: 300,
            scrollOffset: 100,
            breakpoints: {
                sm: 576,
                md: 768,
                lg: 992,
                xl: 1200,
                xxl: 1400
            }
        },
        
        // Initialize theme
        init: function() {
            this.setupThemeToggle();
            this.setupBackToTop();
            this.setupSmoothScrolling();
            this.setupFormEnhancements();
            this.setupTooltips();
            this.setupAnimations();
            this.setupMobileMenu();
            this.setupSearchEnhancements();
            this.setupTableEnhancements();
            this.setupNotifications();
        },

        // Theme toggle functionality
        setupThemeToggle: function() {
            const themeToggle = document.getElementById('theme-toggle');
            if (!themeToggle) return;

            // Initialize theme from localStorage
            const savedTheme = localStorage.getItem('widdx-theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
            themeToggle.checked = savedTheme === 'dark';

            // Handle theme toggle
            themeToggle.addEventListener('change', function() {
                const newTheme = this.checked ? 'dark' : 'light';
                document.documentElement.setAttribute('data-bs-theme', newTheme);
                localStorage.setItem('widdx-theme', newTheme);
                
                // Trigger custom event
                window.dispatchEvent(new CustomEvent('themeChanged', { 
                    detail: { theme: newTheme } 
                }));
            });
        },

        // Back to top button
        setupBackToTop: function() {
            const backToTop = document.getElementById('backToTop');
            if (!backToTop) return;

            // Show/hide based on scroll position
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > WIDDX.config.scrollOffset) {
                    backToTop.classList.add('show');
                } else {
                    backToTop.classList.remove('show');
                }
            });

            // Smooth scroll to top
            backToTop.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        },

        // Smooth scrolling for anchor links
        setupSmoothScrolling: function() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        e.preventDefault();
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        },

        // Form enhancements
        setupFormEnhancements: function() {
            // Auto-focus first input in modals
            document.addEventListener('shown.bs.modal', function(e) {
                const firstInput = e.target.querySelector('input:not([type="hidden"]), textarea, select');
                if (firstInput) {
                    firstInput.focus();
                }
            });

            // Form validation feedback
            document.querySelectorAll('.needs-validation').forEach(form => {
                form.addEventListener('submit', function(e) {
                    if (!form.checkValidity()) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            });

            // Password strength indicator
            document.querySelectorAll('input[type="password"]').forEach(input => {
                if (input.id.includes('password') || input.name.includes('password')) {
                    this.addPasswordStrengthIndicator(input);
                }
            });
        },

        // Add password strength indicator
        addPasswordStrengthIndicator: function(input) {
            const wrapper = document.createElement('div');
            wrapper.className = 'password-strength mt-2';
            wrapper.innerHTML = `
                <div class="progress" style="height: 4px;">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted">Password strength: <span class="strength-text">Weak</span></small>
            `;
            
            input.parentNode.insertBefore(wrapper, input.nextSibling);
            
            const progressBar = wrapper.querySelector('.progress-bar');
            const strengthText = wrapper.querySelector('.strength-text');
            
            input.addEventListener('input', function() {
                const strength = WIDDX.calculatePasswordStrength(this.value);
                progressBar.style.width = strength.percentage + '%';
                progressBar.className = `progress-bar bg-${strength.color}`;
                strengthText.textContent = strength.text;
            });
        },

        // Calculate password strength
        calculatePasswordStrength: function(password) {
            let score = 0;
            if (password.length >= 8) score += 25;
            if (/[a-z]/.test(password)) score += 25;
            if (/[A-Z]/.test(password)) score += 25;
            if (/[0-9]/.test(password)) score += 25;
            if (/[^A-Za-z0-9]/.test(password)) score += 25;
            
            if (score <= 25) return { percentage: 25, color: 'danger', text: 'Weak' };
            if (score <= 50) return { percentage: 50, color: 'warning', text: 'Fair' };
            if (score <= 75) return { percentage: 75, color: 'info', text: 'Good' };
            return { percentage: 100, color: 'success', text: 'Strong' };
        },

        // Initialize tooltips
        setupTooltips: function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        },

        // Setup animations
        setupAnimations: function() {
            // Intersection Observer for fade-in animations
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-fade-in');
                            observer.unobserve(entry.target);
                        }
                    });
                });

                document.querySelectorAll('.feature-item, .quick-action-card, .card').forEach(el => {
                    observer.observe(el);
                });
            }
        },

        // Mobile menu enhancements
        setupMobileMenu: function() {
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');
            
            if (navbarToggler && navbarCollapse) {
                // Close menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (!navbarCollapse.contains(e.target) && !navbarToggler.contains(e.target)) {
                        if (navbarCollapse.classList.contains('show')) {
                            bootstrap.Collapse.getInstance(navbarCollapse).hide();
                        }
                    }
                });

                // Close menu when clicking on links
                navbarCollapse.querySelectorAll('.nav-link').forEach(link => {
                    link.addEventListener('click', function() {
                        if (window.innerWidth < WIDDX.config.breakpoints.lg) {
                            bootstrap.Collapse.getInstance(navbarCollapse).hide();
                        }
                    });
                });
            }
        },

        // Search enhancements
        setupSearchEnhancements: function() {
            const searchInputs = document.querySelectorAll('input[name="domain"], input[type="search"]');
            
            searchInputs.forEach(input => {
                // Add search suggestions
                input.addEventListener('input', function() {
                    if (this.value.length > 2) {
                        // Add your search suggestion logic here
                        console.log('Search suggestions for:', this.value);
                    }
                });

                // Clear button
                if (input.type === 'search') {
                    const clearBtn = document.createElement('button');
                    clearBtn.type = 'button';
                    clearBtn.className = 'btn btn-outline-secondary';
                    clearBtn.innerHTML = '<i class="fas fa-times"></i>';
                    clearBtn.addEventListener('click', function() {
                        input.value = '';
                        input.focus();
                    });
                    
                    if (input.parentNode.classList.contains('input-group')) {
                        input.parentNode.appendChild(clearBtn);
                    }
                }
            });
        },

        // Table enhancements
        setupTableEnhancements: function() {
            // Make tables responsive
            document.querySelectorAll('table:not(.table-responsive table)').forEach(table => {
                if (!table.closest('.table-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });

            // Add sorting functionality
            document.querySelectorAll('th[data-sort]').forEach(th => {
                th.style.cursor = 'pointer';
                th.addEventListener('click', function() {
                    WIDDX.sortTable(this);
                });
            });
        },

        // Simple table sorting
        sortTable: function(th) {
            const table = th.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const index = Array.from(th.parentNode.children).indexOf(th);
            const isAsc = th.classList.contains('sort-asc');
            
            // Remove existing sort classes
            th.parentNode.querySelectorAll('th').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });
            
            // Add new sort class
            th.classList.add(isAsc ? 'sort-desc' : 'sort-asc');
            
            // Sort rows
            rows.sort((a, b) => {
                const aVal = a.children[index].textContent.trim();
                const bVal = b.children[index].textContent.trim();
                
                if (isAsc) {
                    return bVal.localeCompare(aVal, undefined, { numeric: true });
                } else {
                    return aVal.localeCompare(bVal, undefined, { numeric: true });
                }
            });
            
            // Reorder rows
            rows.forEach(row => tbody.appendChild(row));
        },

        // Notification system
        setupNotifications: function() {
            // Auto-hide alerts after 5 seconds
            document.querySelectorAll('.alert:not(.alert-permanent)').forEach(alert => {
                setTimeout(() => {
                    if (alert.parentNode) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 5000);
            });
        },

        // Utility functions
        utils: {
            // Debounce function
            debounce: function(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            },

            // Get current breakpoint
            getCurrentBreakpoint: function() {
                const width = window.innerWidth;
                if (width >= WIDDX.config.breakpoints.xxl) return 'xxl';
                if (width >= WIDDX.config.breakpoints.xl) return 'xl';
                if (width >= WIDDX.config.breakpoints.lg) return 'lg';
                if (width >= WIDDX.config.breakpoints.md) return 'md';
                if (width >= WIDDX.config.breakpoints.sm) return 'sm';
                return 'xs';
            },

            // Format currency
            formatCurrency: function(amount, currency = 'USD') {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: currency
                }).format(amount);
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            WIDDX.init();
        });
    } else {
        WIDDX.init();
    }

    // Make WIDDX globally available
    window.WIDDX = WIDDX;

})();
