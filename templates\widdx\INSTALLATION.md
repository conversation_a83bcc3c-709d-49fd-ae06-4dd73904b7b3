# WIDDX Theme Installation Guide

## Quick Installation Steps

### 1. Pre-Installation Checklist
- [ ] WHMCS 8.x+ installed and running
- [ ] Parent theme "six" is available (default WHMCS theme)
- [ ] Admin access to WHMCS
- [ ] FTP/File manager access to server

### 2. Upload Theme Files
1. Upload the entire `widdx` folder to your WHMCS installation directory:
   ```
   /path/to/whmcs/templates/widdx/
   ```

2. Verify the file structure:
   ```
   templates/widdx/
   ├── theme.yaml
   ├── homepage.tpl
   ├── header.tpl
   ├── footer.tpl
   ├── css/custom.css
   └── assets/img/
   ```

### 3. Replace Placeholder Assets
1. **Logo**: Replace `assets/img/logo.png` with your actual logo
   - Recommended size: 200x60 pixels
   - Format: PNG with transparent background
   - Max height will be 50px (CSS controlled)

2. **Favicon**: Replace `assets/img/favicon.ico` with your favicon
   - Size: 16x16, 32x32, or 48x48 pixels
   - Format: ICO or PNG

### 4. Activate Theme
1. Log into WHMCS Admin Area
2. Navigate to: **Setup → General Settings → General**
3. Find the "Template" dropdown
4. Select "widdx" from the list
5. Click "Save Changes"

### 5. Verify Installation
1. Visit your WHMCS homepage
2. Check that the new design is applied
3. Verify responsive design on mobile devices
4. Test navigation menu functionality
5. Confirm all links work properly

## Customization After Installation

### Update Company Information
Edit `homepage.tpl` to customize:
- Hero banner title and description
- Feature descriptions
- Call-to-action buttons

### Modify Navigation Menu
Edit `header.tpl` around line 100 to customize menu items:
```smarty
<li><a href="{$WEB_ROOT}/your-page.php">Your Page</a></li>
```

### Update Social Media Links
Edit `footer.tpl` to add your social media URLs:
```smarty
<a href="https://facebook.com/yourpage" class="social-link facebook">
```

### Customize Colors
Edit `css/custom.css` to change the color scheme:
```css
:root {
    --widdx-primary: #your-color;
    --widdx-secondary: #your-color;
}
```

## Troubleshooting

### Theme Not Appearing
- Verify files are uploaded to correct directory
- Check file permissions (644 for files, 755 for directories)
- Ensure parent theme "six" exists

### Styling Issues
- Clear browser cache
- Check if custom.css is loading
- Verify CSS syntax in custom.css

### Logo Not Displaying
- Check logo file path and name
- Verify image file format (PNG recommended)
- Ensure proper file permissions

### Mobile Display Issues
- Test on actual devices
- Use browser developer tools
- Check responsive CSS rules

## Advanced Configuration

### Adding Custom Pages
1. Create new .tpl files in the theme directory
2. Follow WHMCS template structure
3. Include proper Smarty syntax

### Integrating with Plugins
- Ensure compatibility with WHMCS version
- Test thoroughly before production use
- Check for CSS conflicts

### Performance Optimization
- Optimize images before upload
- Minify CSS if needed
- Enable GZIP compression on server

## Support and Maintenance

### Regular Updates
- Keep WHMCS updated
- Test theme after WHMCS updates
- Backup theme files before changes

### Backup Recommendations
- Backup entire theme directory
- Keep original files for reference
- Document any customizations made

## Security Considerations

- All directories include index.php protection files
- No direct file access allowed
- Follow WHMCS security guidelines
- Regular security updates

## Final Checklist

After installation, verify:
- [ ] Homepage displays correctly
- [ ] Navigation menu works
- [ ] Footer links are functional
- [ ] Social media links work
- [ ] Mobile responsive design
- [ ] Logo displays properly
- [ ] All pages inherit theme styling
- [ ] No JavaScript errors
- [ ] Fast loading times
- [ ] Cross-browser compatibility

## Getting Help

If you encounter issues:
1. Check this documentation
2. Review WHMCS template documentation
3. Verify file permissions and paths
4. Test with default "six" theme
5. Check server error logs

---

**Note**: This theme is designed as a child theme of "six". If the parent theme is modified or updated, some customizations may need adjustment.
