/* *****************************************************

    ** WIDDX Theme Custom Stylesheet **
    
    A clean and professional WHMCS theme
    Author: <PERSON>
    Version: 1.0.0

***************************************************** */

/* ==========================================================================
   WIDDX Color Variables & Base Styles
   ========================================================================== */

:root {
    --widdx-primary: #2c3e50;
    --widdx-secondary: #3498db;
    --widdx-accent: #e74c3c;
    --widdx-success: #27ae60;
    --widdx-warning: #f39c12;
    --widdx-dark: #34495e;
    --widdx-light: #ecf0f1;
    --widdx-white: #ffffff;
    --widdx-gray: #95a5a6;
    --widdx-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* ==========================================================================
   Header Customizations
   ========================================================================== */

.widdx-header {
    background: var(--widdx-white);
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.widdx-logo-img {
    max-height: 50px;
    width: auto;
}

.widdx-main-menu {
    background: var(--widdx-primary);
    border: none;
}

.widdx-main-menu .navbar-default {
    background: transparent;
    border: none;
}

.widdx-nav > li > a {
    color: var(--widdx-white) !important;
    font-weight: 500;
    padding: 15px 20px;
    transition: all 0.3s ease;
}

.widdx-nav > li > a:hover,
.widdx-nav > li > a:focus {
    background: rgba(255,255,255,0.1) !important;
    color: var(--widdx-white) !important;
}

.widdx-nav > li.dropdown:hover .dropdown-menu {
    display: block;
}

.widdx-nav .dropdown-menu {
    background: var(--widdx-white);
    border: none;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    margin-top: 0;
}

.widdx-nav .dropdown-menu > li > a {
    color: var(--widdx-dark) !important;
    padding: 10px 20px;
}

.widdx-nav .dropdown-menu > li > a:hover {
    background: var(--widdx-light) !important;
    color: var(--widdx-primary) !important;
}

/* ==========================================================================
   Homepage Hero Banner
   ========================================================================== */

.widdx-hero-banner {
    background: var(--widdx-gradient);
    color: var(--widdx-white);
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.widdx-hero-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-actions {
    margin-top: 30px;
}

.widdx-cta-btn {
    background: var(--widdx-accent);
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    margin-right: 15px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.widdx-cta-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--widdx-white);
    color: var(--widdx-white);
    padding: 13px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: var(--widdx-white);
    color: var(--widdx-primary);
}

/* ==========================================================================
   Features Section
   ========================================================================== */

.widdx-features {
    padding: 80px 0;
    background: var(--widdx-light);
}

.section-title {
    font-size: 2.5rem;
    color: var(--widdx-primary);
    margin-bottom: 15px;
    font-weight: 700;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--widdx-gray);
    margin-bottom: 60px;
}

.feature-grid {
    margin-top: 40px;
}

.feature-item {
    text-align: center;
    padding: 40px 20px;
    background: var(--widdx-white);
    border-radius: 10px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.feature-icon {
    font-size: 3rem;
    color: var(--widdx-secondary);
    margin-bottom: 20px;
}

.feature-item h4 {
    color: var(--widdx-primary);
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.feature-item p {
    color: var(--widdx-gray);
    line-height: 1.6;
}

/* ==========================================================================
   Content Sections
   ========================================================================== */

.widdx-content-section {
    padding: 60px 0;
    background: var(--widdx-white);
}

/* ==========================================================================
   Home Banner & Shortcuts
   ========================================================================== */

.widdx-home-banner {
    background: var(--widdx-primary);
    color: var(--widdx-white);
}

.widdx-banner-title {
    color: var(--widdx-white);
    margin-bottom: 30px;
}

.widdx-domain-search .form-control {
    border-radius: 50px 0 0 50px;
    border: none;
    padding: 15px 25px;
    font-size: 1.1rem;
}

.widdx-search-btn,
.widdx-transfer-btn {
    border-radius: 0 50px 50px 0;
    padding: 15px 25px;
    font-weight: 600;
    border: none;
}

.widdx-search-btn {
    background: var(--widdx-success);
}

.widdx-transfer-btn {
    background: var(--widdx-warning);
}

.widdx-shortcuts {
    background: var(--widdx-light);
}

.widdx-help-text {
    color: var(--widdx-dark);
}

.widdx-shortcut-list {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
}

.widdx-shortcut-list li {
    flex: 1;
    min-width: 200px;
}

.widdx-shortcut-item {
    display: block;
    padding: 20px;
    background: var(--widdx-white);
    margin: 10px;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.widdx-shortcut-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    text-decoration: none;
}

/* ==========================================================================
   Footer Customizations
   ========================================================================== */

.widdx-footer {
    background: var(--widdx-dark);
    color: var(--widdx-light);
    padding: 60px 0 20px;
}

.footer-section h4,
.footer-section h5 {
    color: var(--widdx-white);
    margin-bottom: 20px;
    font-weight: 600;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: var(--widdx-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--widdx-secondary);
    text-decoration: none;
}

.social-links {
    display: flex;
    gap: 10px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--widdx-primary);
    color: var(--widdx-white);
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--widdx-secondary);
    color: var(--widdx-white);
    transform: translateY(-2px);
    text-decoration: none;
}

.footer-divider {
    border-color: var(--widdx-primary);
    margin: 40px 0 20px;
}

.footer-bottom {
    padding: 20px 0;
}

.copyright {
    margin: 0;
    color: var(--widdx-gray);
}

.footer-bottom-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 20px;
    justify-content: flex-end;
}

.footer-bottom-links a {
    color: var(--widdx-gray);
    text-decoration: none;
}

.footer-bottom-links a:hover {
    color: var(--widdx-white);
    text-decoration: none;
}

.widdx-back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: var(--widdx-secondary);
    color: var(--widdx-white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    z-index: 1000;
}

.widdx-back-to-top:hover {
    background: var(--widdx-primary);
    color: var(--widdx-white);
    text-decoration: none;
    transform: translateY(-3px);
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .widdx-cta-btn,
    .btn-outline {
        display: block;
        margin: 10px 0;
        width: 100%;
    }
    
    .feature-item {
        margin-bottom: 20px;
    }
    
    .widdx-shortcut-list {
        flex-direction: column;
    }
    
    .footer-bottom-links {
        justify-content: center;
        margin-top: 20px;
    }
}
