# WIDDX Theme Assets

## Logo Requirements

Replace the placeholder `logo.png` file with your actual WIDDX logo:

- **Format**: PNG with transparent background
- **Recommended Size**: 200x60 pixels (or maintain aspect ratio)
- **Max Height**: 50px (as defined in CSS)
- **File Name**: Must be named `logo.png`

## Favicon Requirements

Replace the placeholder `favicon.ico` file with your actual favicon:

- **Format**: ICO format (recommended) or PNG
- **Size**: 16x16, 32x32, or 48x48 pixels
- **File Name**: Must be named `favicon.ico`

## Usage in Templates

The logo is referenced in the header template as:
```smarty
<img src="{$WEB_ROOT}/templates/widdx/assets/img/logo.png" alt="WIDDX - {$companyname}" class="widdx-logo-img">
```

The favicon should be linked in the head section (add to includes/head.tpl if needed):
```html
<link rel="icon" type="image/x-icon" href="{$WEB_ROOT}/templates/widdx/assets/img/favicon.ico">
```

## Additional Assets

You can add more assets to this directory such as:
- Background images
- Icons
- Graphics
- Other theme-related images

Make sure to reference them using the `{$WEB_ROOT}` variable for proper linking.
