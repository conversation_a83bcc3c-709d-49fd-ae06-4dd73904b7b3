/* *****************************************************
   WIDDX Independent Theme - Responsive Styles
   Author: <PERSON>
   Version: 1.0.0
   Description: Mobile-first responsive design rules
***************************************************** */

/* ==========================================================================
   Mobile First Approach - Base styles for mobile devices
   ========================================================================== */

/* Extra Small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    /* Hero Section */
    .widdx-hero {
        min-height: 60vh;
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2rem !important;
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1rem !important;
        margin-bottom: 1.5rem;
    }
    
    .hero-actions {
        flex-direction: column;
        gap: 1rem !important;
    }
    
    .hero-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .trust-number {
        font-size: 1.5rem !important;
    }
    
    /* Navigation */
    .widdx-top-nav {
        padding: 0.25rem 0;
    }
    
    .widdx-main-nav {
        padding: 0.5rem 0;
    }
    
    .widdx-logo {
        max-height: 30px;
    }
    
    .navbar-nav .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    /* Cards and Components */
    .card-body {
        padding: 1rem;
    }
    
    .card-header {
        padding: 1rem 1rem 0.5rem;
    }
    
    /* Quick Actions */
    .quick-action-card .card-body {
        padding: 1.5rem 1rem;
    }
    
    .quick-action-icon {
        margin-bottom: 1rem;
    }
    
    .quick-action-icon i {
        font-size: 2rem !important;
    }
    
    /* Features */
    .feature-item {
        padding: 1.5rem 0.5rem;
        margin-bottom: 2rem;
    }
    
    .feature-icon i {
        font-size: 2rem !important;
    }
    
    /* Tables */
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }
    
    /* Buttons */
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    /* Dashboard */
    .welcome-banner {
        padding: 1.5rem !important;
        text-align: center;
    }
    
    .welcome-stats {
        margin-top: 1rem;
    }
    
    .stat-item {
        margin-bottom: 1rem;
    }
    
    /* Footer */
    .widdx-footer {
        padding: 2rem 0 1rem;
        text-align: center;
    }
    
    .footer-section {
        margin-bottom: 2rem;
    }
    
    .social-links {
        justify-content: center;
        margin-bottom: 1rem;
    }
    
    .footer-bottom-links {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    /* Domain Search */
    .domain-search-widget .input-group-lg .form-control,
    .domain-search-widget .input-group-lg .btn {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }
    
    .domain-search-widget .d-grid {
        gap: 0.5rem !important;
    }
}

/* ==========================================================================
   Small devices (landscape phones, 576px and up)
   ========================================================================== */

@media (min-width: 576px) and (max-width: 767.98px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .hero-actions {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .hero-actions .btn {
        flex: 1;
        min-width: 200px;
        margin: 0 0.5rem 1rem;
    }
    
    .quick-action-card .card-body {
        padding: 2rem 1.5rem;
    }
    
    .feature-item {
        padding: 2rem 1rem;
    }
    
    .welcome-banner .row {
        text-align: center;
    }
    
    .welcome-stats {
        margin-top: 1.5rem;
    }
}

/* ==========================================================================
   Medium devices (tablets, 768px and up)
   ========================================================================== */

@media (min-width: 768px) and (max-width: 991.98px) {
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    .hero-actions .btn {
        min-width: 180px;
    }
    
    .section-title {
        font-size: 2.25rem;
    }
    
    .navbar-expand-lg .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }
    
    .quick-action-card:hover {
        transform: translateY(-3px);
    }
    
    .feature-item:hover {
        transform: translateY(-3px);
    }
    
    /* Dashboard adjustments */
    .stat-icon {
        width: 50px;
        height: 50px;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    /* Footer adjustments */
    .footer-section {
        margin-bottom: 1.5rem;
    }
}

/* ==========================================================================
   Large devices (desktops, 992px and up)
   ========================================================================== */

@media (min-width: 992px) and (max-width: 1199.98px) {
    .hero-title {
        font-size: 3.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.375rem;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
    
    .container {
        max-width: 960px;
    }
    
    /* Sidebar adjustments */
    .widdx-sidebar-container {
        padding-right: 1rem;
    }
    
    /* Card hover effects */
    .card:hover {
        transform: translateY(-2px);
    }
    
    /* Navigation spacing */
    .widdx-main-nav .nav-link {
        margin: 0 0.125rem;
    }
}

/* ==========================================================================
   Extra Large devices (large desktops, 1200px and up)
   ========================================================================== */

@media (min-width: 1200px) {
    .hero-title {
        font-size: 4rem;
    }
    
    .hero-subtitle {
        font-size: 1.5rem;
    }
    
    .section-title {
        font-size: 3rem;
    }
    
    .container {
        max-width: 1140px;
    }
    
    /* Enhanced hover effects for larger screens */
    .quick-action-card:hover {
        transform: translateY(-8px);
    }
    
    .feature-item:hover {
        transform: translateY(-8px);
    }
    
    .card:hover {
        transform: translateY(-3px);
    }
    
    /* Larger spacing for desktop */
    .widdx-hero {
        min-height: 85vh;
    }
    
    .widdx-features {
        padding: 6rem 0;
    }
    
    .widdx-quick-actions {
        padding: 4rem 0;
    }
    
    /* Navigation enhancements */
    .widdx-main-nav .nav-link {
        margin: 0 0.25rem;
        padding: 0.875rem 1.25rem;
    }
    
    /* Dashboard enhancements */
    .welcome-banner {
        padding: 3rem !important;
    }
    
    .stat-icon {
        width: 70px;
        height: 70px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

/* ==========================================================================
   Extra Extra Large devices (larger desktops, 1400px and up)
   ========================================================================== */

@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .hero-title {
        font-size: 4.5rem;
    }
    
    .section-title {
        font-size: 3.5rem;
    }
    
    /* Maximum spacing for very large screens */
    .widdx-hero {
        min-height: 90vh;
    }
    
    .widdx-features {
        padding: 8rem 0;
    }
    
    .feature-item {
        padding: 3rem 1.5rem;
    }
    
    .quick-action-card .card-body {
        padding: 3rem 2rem;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .widdx-top-nav,
    .widdx-main-nav,
    .widdx-footer,
    .back-to-top,
    .btn,
    .hero-actions {
        display: none !important;
    }
    
    .widdx-hero {
        min-height: auto;
        padding: 2rem 0;
        background: white !important;
        color: black !important;
    }
    
    .hero-title,
    .section-title {
        color: black !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* ==========================================================================
   High DPI / Retina Display Optimizations
   ========================================================================== */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .widdx-logo,
    .footer-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
    
    /* Ensure icons remain crisp */
    .fas,
    .fab,
    .far {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* ==========================================================================
   Accessibility Improvements
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .quick-action-card:hover,
    .feature-item:hover,
    .card:hover,
    .btn:hover {
        transform: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid var(--widdx-border) !important;
    }
    
    .btn {
        border-width: 3px !important;
    }
    
    .nav-link {
        border: 1px solid transparent;
    }
    
    .nav-link:hover,
    .nav-link:focus {
        border-color: currentColor;
    }
}
