{* WIDDX Independent Theme - Sidebar Component *}

{if $sidebar && $sidebar->hasChildren()}
    <div class="widdx-sidebar">
        {foreach $sidebar->getChildren() as $item}
            {if $item->getType() eq "header"}
                <h6 class="sidebar-header text-uppercase text-muted fw-bold mb-3 mt-4">
                    {$item->getLabel()}
                </h6>
            {elseif $item->getType() eq "divider"}
                <hr class="sidebar-divider my-3">
            {else}
                {if $item->hasChildren()}
                    <div class="sidebar-item">
                        <div class="sidebar-link collapsed" data-bs-toggle="collapse" 
                             data-bs-target="#sidebar-{$item@index}" aria-expanded="false">
                            {if $item->getClass()}
                                <i class="{$item->getClass()} me-2"></i>
                            {/if}
                            {$item->getLabel()}
                            {if $item->getBadge()}
                                <span class="badge bg-{$item->getBadge()} ms-auto">{$item->getBadgeText()}</span>
                            {/if}
                            <i class="fas fa-chevron-down ms-auto sidebar-arrow"></i>
                        </div>
                        <div class="collapse" id="sidebar-{$item@index}">
                            <div class="sidebar-submenu">
                                {foreach $item->getChildren() as $childItem}
                                    <a href="{$childItem->getUri()}" class="sidebar-sublink"
                                       {if $childItem->getAttribute('target')} target="{$childItem->getAttribute('target')}"{/if}>
                                        {if $childItem->getClass()}
                                            <i class="{$childItem->getClass()} me-2"></i>
                                        {/if}
                                        {$childItem->getLabel()}
                                        {if $childItem->getBadge()}
                                            <span class="badge bg-{$childItem->getBadge()} ms-auto">{$childItem->getBadgeText()}</span>
                                        {/if}
                                    </a>
                                {/foreach}
                            </div>
                        </div>
                    </div>
                {else}
                    <a href="{$item->getUri()}" class="sidebar-link{if $item->isActive()} active{/if}"
                       {if $item->getAttribute('target')} target="{$item->getAttribute('target')}"{/if}>
                        {if $item->getClass()}
                            <i class="{$item->getClass()} me-2"></i>
                        {/if}
                        {$item->getLabel()}
                        {if $item->getBadge()}
                            <span class="badge bg-{$item->getBadge()} ms-auto">{$item->getBadgeText()}</span>
                        {/if}
                    </a>
                {/if}
            {/if}
        {/foreach}
    </div>
{/if}
