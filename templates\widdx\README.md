# WIDDX Independent WHMCS Theme

A fully independent, modern, and responsive WHMCS theme built with Bootstrap 5.3+ and designed for premium hosting businesses.

## 🚀 Features

### ✅ Complete Independence
- **No parent theme dependency** - Fully standalone template
- **All core WHMCS templates included** - Ready for production use
- **Bootstrap 5.3+ integration** - Modern, responsive framework
- **WHMCS 8+ compatibility** - Latest WHMCS features supported

### 🎨 Modern Design
- **Responsive mobile-first design** - Works on all devices
- **Dark/Light theme toggle** - User preference support
- **Professional UI/UX** - Clean, modern interface
- **Custom CSS Grid & Flexbox layouts** - Advanced responsive design
- **FontAwesome 6.4.0 icons** - Comprehensive icon library

### 🛠 Technical Features
- **Local Bootstrap 5.3+ files** - No CDN dependencies
- **Custom CSS with CSS variables** - Easy theme customization
- **Advanced JavaScript functionality** - Enhanced user experience
- **SEO optimized** - Proper meta tags and structure
- **Accessibility compliant** - WCAG guidelines followed
- **Cross-browser compatible** - Chrome, Firefox, Safari, Edge

### 🔧 WHMCS Integration
- **Full client area functionality** - All WHMCS features supported
- **Enhanced shopping cart** - Improved user experience
- **Advanced domain checker** - Real-time validation
- **Support ticket system** - Complete support integration
- **Invoice management** - Professional invoice templates
- **User authentication** - Secure login/registration

## 📁 File Structure

```
templates/widdx/
├── theme.yaml                    # Theme configuration (WHMCS 8+)
├── header.tpl                    # Main header with navigation
├── footer.tpl                    # Footer with links and branding
├── homepage.tpl                  # Landing page with hero section
├── clientareahome.tpl           # Client dashboard
├── clientareadetails.tpl        # Account details page
├── clientareaproducts.tpl       # Products/services list
├── clientareadomains.tpl        # Domain management
├── clientareainvoices.tpl       # Invoice listing
├── viewinvoice.tpl              # Individual invoice view
├── clientareaaddfunds.tpl       # Add funds page
├── supportticketslist.tpl       # Support ticket listing
├── submitticket.tpl             # New ticket form
├── viewticket.tpl               # Individual ticket view
├── login.tpl                    # Login form
├── clientregister.tpl           # Registration form
├── pwreset.tpl                  # Password reset
├── announcements.tpl            # News/announcements list
├── viewannouncement.tpl         # Individual announcement
├── knowledgebase.tpl            # KB categories
├── knowledgebasearticle.tpl     # KB article view
├── css/
│   ├── bootstrap.min.css        # Bootstrap 5.3+ local copy
│   ├── style.css                # Custom theme styles
│   └── responsive.css           # Mobile responsive rules
├── js/
│   ├── bootstrap.bundle.min.js  # Bootstrap JS with Popper
│   ├── theme.js                 # Custom theme functionality
│   └── whmcs-integration.js     # WHMCS-specific enhancements
├── assets/
│   └── img/
│       ├── logo.png             # WIDDX logo placeholder
│       ├── favicon.ico          # Site favicon
│       └── hero-bg.jpg          # Hero section background
├── includes/
│   ├── head.tpl                 # HTML head section
│   ├── navbar.tpl               # Navigation bar component
│   ├── sidebar.tpl              # Sidebar component
│   ├── pageheader.tpl           # Page header component
│   └── breadcrumb.tpl           # Breadcrumb navigation
└── index.php                    # Directory protection
```

## 🚀 Installation

### 1. Upload Theme Files
Upload the entire `widdx` folder to your WHMCS templates directory:
```
/path/to/whmcs/templates/widdx/
```

### 2. Replace Placeholder Assets
- Replace `assets/img/logo.png` with your actual logo (200x60px recommended)
- Replace `assets/img/favicon.ico` with your favicon (32x32px)
- Replace `assets/img/hero-bg.jpg` with your hero background image

### 3. Activate Theme
1. Log into WHMCS Admin Area
2. Go to **Setup → General Settings → General**
3. Change "Template" to "widdx"
4. Save Changes

### 4. Verify Installation
- Visit your WHMCS homepage
- Check responsive design on mobile devices
- Test all client area functionality
- Verify theme toggle works correctly

## 🎨 Customization

### Colors
The theme uses CSS custom properties for easy color customization:

```css
:root {
    --widdx-primary: #2c3e50;    /* Main brand color */
    --widdx-secondary: #3498db;  /* Secondary accent */
    --widdx-accent: #e74c3c;     /* Call-to-action color */
    --widdx-success: #27ae60;    /* Success messages */
    --widdx-warning: #f39c12;    /* Warning messages */
}
```

### Navigation Menu
Edit the navigation menu in `header.tpl` around line 150:
```smarty
<li class="nav-item">
    <a class="nav-link" href="{$WEB_ROOT}/your-page.php">Your Page</a>
</li>
```

### Social Media Links
Update social media links in `footer.tpl`:
```smarty
<a href="https://facebook.com/yourpage" class="social-link facebook">
    <i class="fab fa-facebook-f"></i>
</a>
```

### Hero Section
Modify the hero banner content in `homepage.tpl`:
```smarty
<h1 class="hero-title display-4 fw-bold mb-4">
    Welcome to <span class="text-primary">Your Company</span>
</h1>
```

## 🔧 Advanced Configuration

### Theme Toggle
The dark/light theme toggle is automatically enabled. Users' preferences are saved in localStorage.

### Performance Optimization
- All CSS and JS files are minified
- Images should be optimized before upload
- Enable GZIP compression on your server

### SEO Optimization
- Update meta descriptions in `includes/head.tpl`
- Customize Open Graph tags for social sharing
- Add structured data markup as needed

## 🌐 Browser Support

- **Chrome** (latest)
- **Firefox** (latest)
- **Safari** (latest)
- **Edge** (latest)
- **Internet Explorer** 11+ (limited support)

## 📱 Mobile Support

- **iOS Safari** (iOS 12+)
- **Chrome Mobile** (Android 8+)
- **Samsung Internet**
- **Opera Mobile**

## 🛡 Security Features

- All directories protected with index.php files
- CSRF token integration
- XSS protection through proper template escaping
- Secure asset loading

## 🔍 Troubleshooting

### Theme Not Appearing
- Verify files are uploaded to correct directory
- Check file permissions (644 for files, 755 for directories)
- Ensure WHMCS cache is cleared

### Styling Issues
- Clear browser cache
- Check if custom.css is loading
- Verify CSS syntax in custom files

### JavaScript Errors
- Check browser console for errors
- Ensure jQuery is loaded before theme scripts
- Verify Bootstrap JavaScript is included

### Mobile Display Issues
- Test on actual devices
- Use browser developer tools
- Check responsive CSS rules

## 📞 Support

For theme support and customization:
- Review this documentation
- Check WHMCS template documentation
- Test with default WHMCS themes first
- Verify WHMCS version compatibility

## 📝 Changelog

### Version 1.0.0
- Initial release
- Complete independent theme structure
- Bootstrap 5.3+ integration
- Dark/light theme toggle
- Responsive design implementation
- All core WHMCS templates included
- Modern JavaScript enhancements
- SEO and accessibility optimizations

## 📄 License

This theme is provided as-is for use with WHMCS installations. Modify and customize as needed for your business requirements.

---

**WIDDX Theme** - Professional hosting solutions deserve professional design.
