# WIDDX Theme for WHMCS

A clean and professional WHMCS child theme based on the "six" parent theme, tailored for reseller businesses.

## Theme Information

- **Name**: WIDDX
- **Version**: 1.0.0
- **Author**: <PERSON>
- **Parent Theme**: six
- **WHMCS Compatibility**: 8.x+
- **Description**: A clean and professional WHMCS theme tailored for a reseller business

## Features

### Design Features
- Modern, responsive design
- Clean and professional appearance
- Custom hero banner with call-to-action
- Feature showcase section
- Social media integration
- Custom navigation menu
- Professional footer with multiple sections

### Technical Features
- Child theme inheritance from "six"
- Bootstrap 3.4.1 compatible
- FontAwesome 5.10.1 icons
- jQuery 1.12.4 support
- Responsive design for all devices
- SEO-friendly structure
- Fast loading optimized CSS

## File Structure

```
templates/widdx/
├── theme.yaml              # Theme configuration
├── homepage.tpl            # Custom homepage template
├── header.tpl              # Custom header template
├── footer.tpl              # Custom footer template
├── index.php               # Security protection
├── README.md               # This documentation
├── css/
│   ├── custom.css          # Custom theme styles
│   └── index.php           # Security protection
└── assets/
    ├── index.php           # Security protection
    └── img/
        ├── logo.png        # Theme logo (placeholder)
        ├── favicon.ico     # Site favicon (placeholder)
        ├── README.md       # Asset documentation
        └── index.php       # Security protection
```

## Installation Instructions

1. **Upload Theme Files**
   - Upload the entire `widdx` folder to `/whmcs/templates/`
   - Ensure all files and directories are properly uploaded

2. **Replace Placeholder Assets**
   - Replace `assets/img/logo.png` with your actual logo
   - Replace `assets/img/favicon.ico` with your actual favicon
   - See `assets/img/README.md` for detailed requirements

3. **Activate Theme**
   - Log into WHMCS Admin Area
   - Go to Setup > General Settings > General
   - Change "Template" to "widdx"
   - Save Changes

4. **Customize Content**
   - Update social media links in `footer.tpl`
   - Modify navigation menu items in `header.tpl`
   - Customize hero banner content in `homepage.tpl`
   - Adjust colors and styling in `css/custom.css`

## Customization Guide

### Colors
The theme uses CSS custom properties (variables) for easy color customization:

```css
:root {
    --widdx-primary: #2c3e50;    /* Main brand color */
    --widdx-secondary: #3498db;  /* Secondary accent */
    --widdx-accent: #e74c3c;     /* Call-to-action color */
    --widdx-success: #27ae60;    /* Success messages */
    --widdx-warning: #f39c12;    /* Warning messages */
}
```

### Navigation Menu
Edit the navigation menu in `header.tpl` around line 100:

```smarty
<ul class="nav navbar-nav widdx-nav">
    <li><a href="{$WEB_ROOT}/index.php">Home</a></li>
    <li><a href="{$WEB_ROOT}/cart.php">Hosting Plans</a></li>
    <li><a href="{$WEB_ROOT}/submitticket.php">Support</a></li>
    <li><a href="{$WEB_ROOT}/contact.php">Contact</a></li>
</ul>
```

### Social Media Links
Update social media links in `footer.tpl` around line 50:

```smarty
<a href="https://facebook.com/yourpage" class="social-link facebook">
    <i class="fab fa-facebook-f"></i>
</a>
```

### Hero Banner Content
Modify the hero banner text in `homepage.tpl`:

```smarty
<h1 class="hero-title">Welcome to WIDDX</h1>
<p class="hero-subtitle">Your custom description here...</p>
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Performance

- Optimized CSS with minimal overhead
- Efficient use of Bootstrap framework
- Fast loading times
- Mobile-first responsive design

## Security

- All directories protected with index.php files
- No direct file access allowed
- Follows WHMCS security best practices
- XSS protection through proper template escaping

## Support

For theme support and customization:
- Review this documentation
- Check WHMCS template documentation
- Ensure parent theme "six" is available
- Verify WHMCS version compatibility

## Changelog

### Version 1.0.0
- Initial release
- Custom homepage with hero banner
- Professional header and footer
- Responsive design implementation
- Social media integration
- Modern CSS styling with custom properties

## License

This theme is provided as-is for use with WHMCS installations. Modify and customize as needed for your business requirements.
