/* *****************************************************
   WIDDX Independent Theme - Main Stylesheet
   Author: <PERSON>
   Version: 1.0.0
   Description: Modern, responsive WHMCS theme
***************************************************** */

/* ==========================================================================
   CSS Custom Properties (Variables)
   ========================================================================== */

:root {
    /* Primary Colors */
    --widdx-primary: #2c3e50;
    --widdx-secondary: #3498db;
    --widdx-accent: #e74c3c;
    --widdx-success: #27ae60;
    --widdx-warning: #f39c12;
    --widdx-danger: #e74c3c;
    --widdx-info: #17a2b8;
    
    /* Neutral Colors */
    --widdx-dark: #34495e;
    --widdx-light: #ecf0f1;
    --widdx-white: #ffffff;
    --widdx-gray: #95a5a6;
    --widdx-gray-100: #f8f9fa;
    --widdx-gray-200: #e9ecef;
    --widdx-gray-300: #dee2e6;
    --widdx-gray-400: #ced4da;
    --widdx-gray-500: #adb5bd;
    --widdx-gray-600: #6c757d;
    --widdx-gray-700: #495057;
    --widdx-gray-800: #343a40;
    --widdx-gray-900: #212529;
    
    /* Theme Colors */
    --widdx-text: #2c3e50;
    --widdx-bg: #ffffff;
    --widdx-border: #dee2e6;
    
    /* Gradients */
    --widdx-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --widdx-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --widdx-gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Shadows */
    --widdx-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --widdx-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --widdx-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Border Radius */
    --widdx-border-radius: 0.375rem;
    --widdx-border-radius-lg: 0.5rem;
    --widdx-border-radius-xl: 1rem;
    
    /* Transitions */
    --widdx-transition: all 0.3s ease;
    --widdx-transition-fast: all 0.15s ease;
}

/* Dark Theme Variables */
[data-bs-theme="dark"] {
    --widdx-text: #ecf0f1;
    --widdx-bg: #2c3e50;
    --widdx-border: #495057;
    --widdx-light: #34495e;
    --widdx-dark: #ecf0f1;
    --widdx-gray-100: #343a40;
    --widdx-gray-200: #495057;
    --widdx-gray-300: #6c757d;
}

/* ==========================================================================
   Base Styles
   ========================================================================== */

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--widdx-text);
    background-color: var(--widdx-bg);
    transition: var(--widdx-transition);
    line-height: 1.6;
}

.widdx-body {
    padding-top: 0;
}

/* ==========================================================================
   Navigation Styles
   ========================================================================== */

.widdx-top-nav {
    background: var(--widdx-white);
    border-bottom: 1px solid var(--widdx-border);
    padding: 0.5rem 0;
    font-size: 0.875rem;
}

.widdx-main-nav {
    background: var(--widdx-primary);
    box-shadow: var(--widdx-shadow);
    padding: 1rem 0;
}

.widdx-main-nav .navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--widdx-white) !important;
}

.widdx-logo {
    max-height: 40px;
    width: auto;
}

.widdx-main-nav .nav-link {
    color: var(--widdx-white) !important;
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: var(--widdx-transition);
    border-radius: var(--widdx-border-radius);
    margin: 0 0.25rem;
}

.widdx-main-nav .nav-link:hover,
.widdx-main-nav .nav-link:focus {
    background: rgba(255, 255, 255, 0.1);
    color: var(--widdx-white) !important;
}

.widdx-main-nav .dropdown-menu {
    background: var(--widdx-white);
    border: none;
    box-shadow: var(--widdx-shadow-lg);
    border-radius: var(--widdx-border-radius);
    margin-top: 0.5rem;
}

.widdx-main-nav .dropdown-item {
    color: var(--widdx-text);
    padding: 0.75rem 1.5rem;
    transition: var(--widdx-transition);
}

.widdx-main-nav .dropdown-item:hover {
    background: var(--widdx-gray-100);
    color: var(--widdx-primary);
}

/* ==========================================================================
   Hero Section
   ========================================================================== */

.widdx-hero {
    position: relative;
    min-height: 80vh;
    display: flex;
    align-items: center;
    background: var(--widdx-gradient-primary);
    color: var(--widdx-white);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.8) 0%, rgba(52, 73, 94, 0.6) 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-actions .btn {
    padding: 0.875rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: var(--widdx-transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--widdx-shadow-lg);
}

.hero-trust {
    margin-top: 3rem;
}

.trust-item {
    text-align: center;
}

.trust-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--widdx-white);
}

.trust-label {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-bottom: 0;
}

/* ==========================================================================
   Domain Search Widget
   ========================================================================== */

.domain-search-widget .card {
    border: none;
    border-radius: var(--widdx-border-radius-lg);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.domain-search-widget .input-group-lg .form-control {
    border-radius: 50px 0 0 50px;
    border: 2px solid var(--widdx-gray-300);
    padding: 1rem 1.5rem;
}

.domain-search-widget .input-group-lg .btn {
    border-radius: 0 50px 50px 0;
    padding: 1rem 1.5rem;
    border: 2px solid var(--widdx-primary);
}

.popular-tlds .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
}

/* ==========================================================================
   Quick Actions Section
   ========================================================================== */

.widdx-quick-actions {
    background: var(--widdx-gray-100);
}

.quick-action-card {
    text-decoration: none;
    color: inherit;
    transition: var(--widdx-transition);
}

.quick-action-card:hover {
    text-decoration: none;
    color: inherit;
    transform: translateY(-5px);
}

.quick-action-card .card {
    transition: var(--widdx-transition);
    border-radius: var(--widdx-border-radius-lg);
}

.quick-action-card:hover .card {
    box-shadow: var(--widdx-shadow-lg);
}

.quick-action-icon {
    margin-bottom: 1rem;
}

/* ==========================================================================
   Features Section
   ========================================================================== */

.widdx-features {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--widdx-primary);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    margin-bottom: 3rem;
}

.feature-item {
    padding: 2rem 1rem;
    transition: var(--widdx-transition);
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    margin-bottom: 1.5rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--widdx-primary);
    margin-bottom: 1rem;
}

.feature-description {
    color: var(--widdx-gray-600);
    line-height: 1.6;
}

/* ==========================================================================
   Dashboard Styles
   ========================================================================== */

.widdx-dashboard {
    padding: 2rem 0;
}

.welcome-banner {
    background: var(--widdx-gradient-primary) !important;
    border-radius: var(--widdx-border-radius-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-number {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--widdx-primary);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--widdx-gray-600);
}

/* ==========================================================================
   Card Styles
   ========================================================================== */

.card {
    border: none;
    border-radius: var(--widdx-border-radius-lg);
    box-shadow: var(--widdx-shadow-sm);
    transition: var(--widdx-transition);
}

.card:hover {
    box-shadow: var(--widdx-shadow);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid var(--widdx-border);
    padding: 1.5rem 1.5rem 1rem;
}

.card-body {
    padding: 1.5rem;
}

/* ==========================================================================
   Button Styles
   ========================================================================== */

.btn {
    border-radius: var(--widdx-border-radius);
    font-weight: 500;
    transition: var(--widdx-transition);
    border-width: 2px;
}

.btn-primary {
    background: var(--widdx-primary);
    border-color: var(--widdx-primary);
}

.btn-primary:hover {
    background: var(--widdx-dark);
    border-color: var(--widdx-dark);
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: var(--widdx-primary);
    border-color: var(--widdx-primary);
}

.btn-outline-primary:hover {
    background: var(--widdx-primary);
    border-color: var(--widdx-primary);
    transform: translateY(-1px);
}

/* ==========================================================================
   Table Styles
   ========================================================================== */

.table {
    border-radius: var(--widdx-border-radius);
    overflow: hidden;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--widdx-primary);
    background: var(--widdx-gray-100);
}

.table-hover tbody tr:hover {
    background: var(--widdx-gray-100);
}

/* ==========================================================================
   Badge Styles
   ========================================================================== */

.badge {
    font-weight: 500;
    border-radius: 50px;
    padding: 0.375rem 0.75rem;
}

/* ==========================================================================
   Footer Styles
   ========================================================================== */

.widdx-footer {
    background: var(--widdx-dark);
    color: var(--widdx-light);
    padding: 4rem 0 2rem;
    margin-top: 4rem;
}

.footer-logo {
    max-height: 40px;
    width: auto;
}

.footer-title {
    color: var(--widdx-white);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.footer-text {
    color: var(--widdx-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--widdx-light);
    text-decoration: none;
    transition: var(--widdx-transition);
}

.footer-links a:hover {
    color: var(--widdx-secondary);
}

.social-links {
    display: flex;
    gap: 0.75rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--widdx-primary);
    color: var(--widdx-white);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--widdx-transition);
}

.social-link:hover {
    background: var(--widdx-secondary);
    color: var(--widdx-white);
    transform: translateY(-2px);
}

.footer-divider {
    border-color: var(--widdx-primary);
    margin: 2rem 0 1.5rem;
}

.footer-bottom-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 1.5rem;
}

.footer-bottom-links a {
    color: var(--widdx-gray);
    text-decoration: none;
    font-size: 0.875rem;
}

.footer-bottom-links a:hover {
    color: var(--widdx-white);
}

.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--widdx-secondary);
    color: var(--widdx-white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--widdx-transition);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--widdx-primary);
    transform: translateY(-3px);
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .hero-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .widdx-main-nav .nav-link {
        padding: 0.5rem 1rem;
        margin: 0;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .footer-bottom-links {
        justify-content: center;
        flex-wrap: wrap;
    }
}
