{* WIDDX Independent Theme - Page Header Component *}

<div class="widdx-page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                {if $title}
                    <h1 class="page-title mb-2">
                        {$title}
                        {if $desc}
                            <small class="text-muted d-block fs-6 fw-normal mt-1">{$desc}</small>
                        {/if}
                    </h1>
                {/if}
            </div>
            <div class="col-md-4 text-end">
                {if $showbreadcrumb && $breadcrumb}
                    {include file="$template/includes/breadcrumb.tpl"}
                {/if}
            </div>
        </div>
    </div>
</div>
