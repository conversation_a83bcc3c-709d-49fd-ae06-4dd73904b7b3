{* WIDDX Independent Theme - Client Area Dashboard *}

{include file="$template/header.tpl"}

<div class="widdx-dashboard">
    <div class="container-fluid">
        {* Welcome Section *}
        <div class="row mb-4">
            <div class="col-12">
                <div class="welcome-banner bg-gradient-primary text-white rounded p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">Welcome back, {$clientsdetails.firstname}!</h2>
                            <p class="mb-0 opacity-75">Manage your services, view invoices, and get support from your dashboard.</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="welcome-stats">
                                <div class="stat-item d-inline-block me-3">
                                    <div class="stat-number">{$clientsstats.productsnumactive}</div>
                                    <div class="stat-label">Active Services</div>
                                </div>
                                <div class="stat-item d-inline-block">
                                    <div class="stat-number">{$clientsstats.numoverdueinvoices}</div>
                                    <div class="stat-label">Overdue Invoices</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {* Quick Stats Cards *}
        <div class="row g-3 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="stat-icon bg-primary bg-opacity-10 text-primary rounded-circle p-3">
                                    <i class="fas fa-server fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="stat-number h4 mb-0">{$clientsstats.productsnumactive}</div>
                                <div class="stat-label text-muted">Active Services</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="stat-icon bg-success bg-opacity-10 text-success rounded-circle p-3">
                                    <i class="fas fa-globe fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="stat-number h4 mb-0">{$clientsstats.numactivedomains}</div>
                                <div class="stat-label text-muted">Active Domains</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="stat-icon bg-warning bg-opacity-10 text-warning rounded-circle p-3">
                                    <i class="fas fa-file-invoice fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="stat-number h4 mb-0">{$clientsstats.numunpaidinvoices}</div>
                                <div class="stat-label text-muted">Unpaid Invoices</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="stat-icon bg-info bg-opacity-10 text-info rounded-circle p-3">
                                    <i class="fas fa-ticket-alt fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="stat-number h4 mb-0">{$clientsstats.numactivetickets}</div>
                                <div class="stat-label text-muted">Open Tickets</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            {* Recent Services *}
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-transparent border-0 pb-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Recent Services</h5>
                            <a href="clientarea.php?action=products" class="btn btn-outline-primary btn-sm">
                                View All <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        {if $services}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Service</th>
                                            <th>Status</th>
                                            <th>Next Due</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {foreach $services as $service}
                                            {if $service@index < 5}
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="service-icon me-2">
                                                                <i class="fas fa-{if $service.type eq 'hostingaccount'}server{elseif $service.type eq 'reselleraccount'}users{elseif $service.type eq 'server'}hdd{else}box{/if} text-primary"></i>
                                                            </div>
                                                            <div>
                                                                <div class="fw-semibold">{$service.product}</div>
                                                                <small class="text-muted">{$service.domain}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{if $service.status eq 'Active'}success{elseif $service.status eq 'Suspended'}warning{elseif $service.status eq 'Terminated'}danger{else}secondary{/if}">
                                                            {$service.status}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        {if $service.nextduedate}
                                                            {$service.nextduedate}
                                                        {else}
                                                            <span class="text-muted">N/A</span>
                                                        {/if}
                                                    </td>
                                                    <td>
                                                        <a href="clientarea.php?action=productdetails&id={$service.id}" 
                                                           class="btn btn-outline-primary btn-sm">
                                                            Manage
                                                        </a>
                                                    </td>
                                                </tr>
                                            {/if}
                                        {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        {else}
                            <div class="text-center py-4">
                                <i class="fas fa-server fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Services Yet</h5>
                                <p class="text-muted">Get started by ordering your first hosting service.</p>
                                <a href="{$WEB_ROOT}/cart.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Order Now
                                </a>
                            </div>
                        {/if}
                    </div>
                </div>

                {* Recent Invoices *}
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent border-0 pb-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Recent Invoices</h5>
                            <a href="clientarea.php?action=invoices" class="btn btn-outline-primary btn-sm">
                                View All <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        {if $invoices}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Invoice #</th>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {foreach $invoices as $invoice}
                                            {if $invoice@index < 5}
                                                <tr>
                                                    <td>
                                                        <span class="fw-semibold">#{$invoice.invoicenum}</span>
                                                    </td>
                                                    <td>{$invoice.dateCreated}</td>
                                                    <td>
                                                        <span class="fw-semibold">{$invoice.total}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{if $invoice.status eq 'Paid'}success{elseif $invoice.status eq 'Unpaid'}warning{elseif $invoice.status eq 'Overdue'}danger{else}secondary{/if}">
                                                            {$invoice.status}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a href="viewinvoice.php?id={$invoice.id}" 
                                                           class="btn btn-outline-primary btn-sm">
                                                            View
                                                        </a>
                                                        {if $invoice.status eq 'Unpaid' || $invoice.status eq 'Overdue'}
                                                            <a href="viewinvoice.php?id={$invoice.id}" 
                                                               class="btn btn-success btn-sm ms-1">
                                                                Pay Now
                                                            </a>
                                                        {/if}
                                                    </td>
                                                </tr>
                                            {/if}
                                        {/foreach}
                                    </tbody>
                                </table>
                            </div>
                        {else}
                            <div class="text-center py-4">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Invoices Yet</h5>
                                <p class="text-muted">Your invoices will appear here once you place an order.</p>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>

            {* Sidebar *}
            <div class="col-lg-4">
                {* Quick Actions *}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{$WEB_ROOT}/cart.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Order New Service
                            </a>
                            <a href="submitticket.php" class="btn btn-outline-primary">
                                <i class="fas fa-life-ring me-2"></i>Open Support Ticket
                            </a>
                            <a href="clientarea.php?action=addfunds" class="btn btn-outline-success">
                                <i class="fas fa-credit-card me-2"></i>Add Funds
                            </a>
                            <a href="clientarea.php?action=details" class="btn btn-outline-info">
                                <i class="fas fa-user me-2"></i>Update Profile
                            </a>
                        </div>
                    </div>
                </div>

                {* Account Information *}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">Account Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="account-info">
                            <div class="info-item mb-3">
                                <label class="text-muted small">Client ID</label>
                                <div class="fw-semibold">#{$clientsdetails.userid}</div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="text-muted small">Email</label>
                                <div class="fw-semibold">{$clientsdetails.email}</div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="text-muted small">Credit Balance</label>
                                <div class="fw-semibold text-success">{$clientsdetails.credit}</div>
                            </div>
                            <div class="info-item">
                                <label class="text-muted small">Last Login</label>
                                <div class="fw-semibold">{$lastlogin}</div>
                            </div>
                        </div>
                    </div>
                </div>

                {* Recent Tickets *}
                {if $tickets}
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">Recent Tickets</h5>
                                <a href="supporttickets.php" class="btn btn-outline-primary btn-sm">
                                    View All
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            {foreach $tickets as $ticket}
                                {if $ticket@index < 3}
                                    <div class="ticket-item mb-3 pb-3 {if !$ticket@last}border-bottom{/if}">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">
                                                    <a href="viewticket.php?tid={$ticket.tid}" class="text-decoration-none">
                                                        #{$ticket.tid} - {$ticket.subject}
                                                    </a>
                                                </h6>
                                                <small class="text-muted">{$ticket.date}</small>
                                            </div>
                                            <span class="badge bg-{if $ticket.status eq 'Open'}success{elseif $ticket.status eq 'Answered'}primary{elseif $ticket.status eq 'Customer-Reply'}warning{else}secondary{/if} ms-2">
                                                {$ticket.status}
                                            </span>
                                        </div>
                                    </div>
                                {/if}
                            {/foreach}
                        </div>
                    </div>
                {/if}
            </div>
        </div>
    </div>
</div>

{include file="$template/footer.tpl"}
